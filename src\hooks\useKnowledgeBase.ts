import { useState, useEffect, useCallback } from 'react';
import { apiClient, KnowledgeBase, CreateKnowledgeBaseRequest } from '../services/api';

export const useKnowledgeBase = () => {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchKnowledgeBases = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getKnowledgeBases();
      if (response.success && response.data) {
        // Sort knowledge bases by creation date (newest first)
        const sortedKBs = response.data.sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        setKnowledgeBases(sortedKBs);
      } else {
        setError(response.error || 'Failed to fetch knowledge bases');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch knowledge bases');
    } finally {
      setLoading(false);
    }
  }, []);

  const getKnowledgeBase = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getKnowledgeBase(id);
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get knowledge base');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get knowledge base';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const createKnowledgeBase = useCallback(async (knowledgeBase: CreateKnowledgeBaseRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.createKnowledgeBase(knowledgeBase);
      if (response.success && response.data) {
        setKnowledgeBases(prev => [response.data!, ...prev]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create knowledge base');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create knowledge base';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const updateKnowledgeBase = useCallback(async (id: string, updates: Partial<CreateKnowledgeBaseRequest>) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.updateKnowledgeBase(id, updates);
      if (response.success && response.data) {
        setKnowledgeBases(prev => 
          prev.map(kb => 
            kb.id === id ? response.data! : kb
          )
        );
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to update knowledge base');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update knowledge base';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteKnowledgeBase = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.deleteKnowledgeBase(id);
      if (response.success) {
        setKnowledgeBases(prev => prev.filter(kb => kb.id !== id));
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete knowledge base');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete knowledge base';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Helper functions
  const getKnowledgeBasesByProvider = useCallback((provider: 'trieve' | 'google') => {
    return knowledgeBases.filter(kb => kb.provider === provider);
  }, [knowledgeBases]);

  const getProviderIcon = useCallback((provider: 'trieve' | 'google') => {
    switch (provider) {
      case 'trieve':
        return '🔍';
      case 'google':
        return '🔍';
      default:
        return '📚';
    }
  }, []);

  const getProviderColor = useCallback((provider: 'trieve' | 'google') => {
    switch (provider) {
      case 'trieve':
        return 'bg-blue-500/20 text-blue-400';
      case 'google':
        return 'bg-red-500/20 text-red-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  }, []);

  const getSearchTypeIcon = useCallback((searchType: 'semantic' | 'fulltext' | 'hybrid') => {
    switch (searchType) {
      case 'semantic':
        return '🧠';
      case 'fulltext':
        return '📝';
      case 'hybrid':
        return '🔀';
      default:
        return '🔍';
    }
  }, []);

  const getSearchTypeColor = useCallback((searchType: 'semantic' | 'fulltext' | 'hybrid') => {
    switch (searchType) {
      case 'semantic':
        return 'bg-purple-500/20 text-purple-400';
      case 'fulltext':
        return 'bg-green-500/20 text-green-400';
      case 'hybrid':
        return 'bg-orange-500/20 text-orange-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  }, []);

  const formatKnowledgeBaseName = useCallback((kb: KnowledgeBase) => {
    return kb.name || `Knowledge Base ${kb.id.slice(0, 8)}`;
  }, []);

  const getFileCount = useCallback((kb: KnowledgeBase) => {
    if (!kb.createPlan?.chunkPlans) return 0;
    return kb.createPlan.chunkPlans.reduce((total, plan) => total + plan.fileIds.length, 0);
  }, []);

  const getWebsiteCount = useCallback((kb: KnowledgeBase) => {
    if (!kb.createPlan?.chunkPlans) return 0;
    return kb.createPlan.chunkPlans.reduce((total, plan) => total + (plan.websites?.length || 0), 0);
  }, []);

  const createDefaultKnowledgeBase = useCallback((name: string, fileIds: string[] = []): CreateKnowledgeBaseRequest => {
    return {
      name,
      provider: 'trieve',
      searchPlan: {
        searchType: 'semantic',
        topK: 5,
        removeStopWords: true,
        scoreThreshold: 0.7,
      },
      createPlan: {
        type: 'create',
        chunkPlans: fileIds.length > 0 ? [{
          fileIds,
          targetSplitsPerChunk: 50,
          splitDelimiters: ['.', '!', '?', '\n'],
          rebalanceChunks: true,
        }] : [],
      },
    };
  }, []);

  const validateKnowledgeBase = useCallback((kb: Partial<CreateKnowledgeBaseRequest>) => {
    const errors: string[] = [];
    
    if (!kb.name?.trim()) {
      errors.push('Name is required');
    }
    
    if (!kb.provider) {
      errors.push('Provider is required');
    }
    
    if (!kb.searchPlan?.searchType) {
      errors.push('Search type is required');
    }
    
    if (kb.searchPlan?.topK && (kb.searchPlan.topK < 1 || kb.searchPlan.topK > 20)) {
      errors.push('Top K must be between 1 and 20');
    }
    
    if (kb.searchPlan?.scoreThreshold && (kb.searchPlan.scoreThreshold < 0 || kb.searchPlan.scoreThreshold > 1)) {
      errors.push('Score threshold must be between 0 and 1');
    }
    
    return errors;
  }, []);

  useEffect(() => {
    fetchKnowledgeBases();
  }, [fetchKnowledgeBases]);

  return {
    knowledgeBases,
    loading,
    error,
    fetchKnowledgeBases,
    getKnowledgeBase,
    createKnowledgeBase,
    updateKnowledgeBase,
    deleteKnowledgeBase,
    getKnowledgeBasesByProvider,
    getProviderIcon,
    getProviderColor,
    getSearchTypeIcon,
    getSearchTypeColor,
    formatKnowledgeBaseName,
    getFileCount,
    getWebsiteCount,
    createDefaultKnowledgeBase,
    validateKnowledgeBase,
    clearError: () => setError(null),
  };
};
