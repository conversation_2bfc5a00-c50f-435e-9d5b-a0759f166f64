import React from 'react';
import { ChevronDown } from 'lucide-react';

interface NavItemProps {
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  hasSubmenu?: boolean;
  onClick?: () => void;
  isCollapsed?: boolean;
}

export const NavItem: React.FC<NavItemProps> = ({ 
  icon, 
  label, 
  active = false,
  hasSubmenu = false,
  onClick,
  isCollapsed = false
}) => {
  return (
    <div 
      className={`flex items-center ${isCollapsed ? 'justify-center' : 'px-4'} py-2 text-sm cursor-pointer hover:bg-gray-800 transition-colors duration-200 ${
        active ? 'bg-gray-800' : ''
      }`}
      onClick={onClick}
    >
      <div className={`w-6 h-6 flex items-center justify-center ${isCollapsed ? '' : 'mr-3'} text-gray-400`}>
        {icon}
      </div>
      {!isCollapsed && (
        <>
          <span className={active ? 'font-medium' : ''}>{label}</span>
          {hasSubmenu && (
        <ChevronDown size={16} className="ml-auto text-gray-500" />
          )}
        </>
      )}
    </div>
  );
};