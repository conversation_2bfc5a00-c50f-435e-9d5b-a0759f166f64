import { Router, Request, Response } from 'express';
import { vapiServices } from '../../api/services/index.js';
import { AnalyticsRequest } from '../../api/types.js';

const router = Router();

/**
 * POST /api/analytics/query
 * Execute custom analytics queries
 */
router.post('/query', async (req: Request, res: Response) => {
  try {
    const analyticsRequest: AnalyticsRequest = req.body;
    const result = await vapiServices.analytics.executeQuery(analyticsRequest);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error executing analytics query:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute analytics query',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/analytics/calls
 * Get call statistics
 */
router.get('/calls', async (req: Request, res: Response) => {
  try {
    const dateRange = req.query.start && req.query.end ? {
      start: req.query.start as string,
      end: req.query.end as string,
    } : undefined;

    const assistantId = req.query.assistantId as string;
    const phoneNumberId = req.query.phoneNumberId as string;

    const result = await vapiServices.analytics.getCallStats(dateRange, assistantId, phoneNumberId);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting call statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get call statistics',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/analytics/leads
 * Get lead attribution and campaign data
 */
router.get('/leads', async (req: Request, res: Response) => {
  try {
    const dateRange = req.query.start && req.query.end ? {
      start: req.query.start as string,
      end: req.query.end as string,
    } : undefined;

    const campaignId = req.query.campaignId as string;

    const result = await vapiServices.analytics.getLeadStats(dateRange, campaignId);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting lead statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get lead statistics',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/analytics/agents/:id
 * Get agent-specific success metrics
 */
router.get('/agents/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const dateRange = req.query.start && req.query.end ? {
      start: req.query.start as string,
      end: req.query.end as string,
    } : undefined;

    const result = await vapiServices.analytics.getAgentMetrics(id, dateRange);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting agent metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get agent metrics',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/analytics/dashboard
 * Get real-time dashboard data
 */
router.get('/dashboard', async (req: Request, res: Response) => {
  try {
    const result = await vapiServices.analytics.getDashboardData();

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting dashboard data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get dashboard data',
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
