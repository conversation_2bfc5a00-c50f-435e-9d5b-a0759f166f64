import React, { useState } from 'react';
import { Phone, Plus, MoreHorizontal, Search, AlertCircle, Trash2, Edit, Settings } from 'lucide-react';
import { Modal } from '../components/Modal';
import { ApiKeySetup } from '../components/ApiKeySetup';
import { usePhoneNumbers } from '../hooks/usePhoneNumbers';
import { useAssistants } from '../hooks/useAssistants';
import { PhoneNumber } from '../services/api';

export const PhoneNumbers: React.FC = () => {
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedPhoneNumber, setSelectedPhoneNumber] = useState<PhoneNumber | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [hasApiKey, setHasApiKey] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [phoneNumberName, setPhoneNumberName] = useState('');
  const [selectedAssistant, setSelectedAssistant] = useState('');

  // Check for API key on mount
  React.useEffect(() => {
    const apiKey = import.meta.env.VITE_VAPI_API_KEY || localStorage.getItem('vapi_api_key');
    setHasApiKey(!!apiKey);
  }, []);

  const {
    phoneNumbers: vapiPhoneNumbers,
    loading,
    error,
    createPhoneNumber,
    updatePhoneNumber,
    deletePhoneNumber,
    clearError,
  } = usePhoneNumbers();

  const { assistants } = useAssistants();

  // Filter phone numbers based on search query
  const filteredPhoneNumbers = vapiPhoneNumbers.filter(number =>
    number.number.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (number.name && number.name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Handle phone number creation
  const handleCreatePhoneNumber = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!phoneNumber.trim()) return;

    try {
      await createPhoneNumber({
        provider: 'vapi',
        number: phoneNumber,
        name: phoneNumberName || undefined,
        assistantId: selectedAssistant || undefined,
        numberE164CheckEnabled: true,
      });

      setIsImportModalOpen(false);
      setPhoneNumber('');
      setPhoneNumberName('');
      setSelectedAssistant('');
    } catch (err) {
      console.error('Failed to create phone number:', err);
    }
  };

  // Handle phone number deletion
  const handleDeletePhoneNumber = async (id: string) => {
    try {
      await deletePhoneNumber(id);
      setShowDeleteConfirm(null);
    } catch (err) {
      console.error('Failed to delete phone number:', err);
    }
  };

  // Handle phone number editing
  const handleEditPhoneNumber = (phoneNumber: PhoneNumber) => {
    setSelectedPhoneNumber(phoneNumber);
    setPhoneNumberName(phoneNumber.name || '');
    setSelectedAssistant(phoneNumber.assistantId || '');
    setIsEditModalOpen(true);
  };

  // Handle phone number update
  const handleUpdatePhoneNumber = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPhoneNumber) return;

    try {
      await updatePhoneNumber(selectedPhoneNumber.id, {
        name: phoneNumberName || undefined,
        assistantId: selectedAssistant || undefined,
      });

      setIsEditModalOpen(false);
      setSelectedPhoneNumber(null);
      setPhoneNumberName('');
      setSelectedAssistant('');
    } catch (err) {
      console.error('Failed to update phone number:', err);
    }
  };

  // Show API key setup if no key is found
  if (!hasApiKey) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0F0F0F] text-white p-6">
        <ApiKeySetup onApiKeySet={() => setHasApiKey(true)} />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold mb-1">Phone Numbers</h1>
            <p className="text-gray-400">Manage your Vapi phone numbers</p>
          </div>
          <button
            onClick={() => setIsImportModalOpen(true)}
            className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
          >
            <Plus size={16} />
            Add number
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-900/20 border border-red-800 rounded-lg flex items-center gap-2">
            <AlertCircle size={18} className="text-red-400" />
            <span className="text-red-400">{error}</span>
            <button
              onClick={clearError}
              className="ml-auto text-red-400 hover:text-red-300"
            >
              ×
            </button>
          </div>
        )}

        {/* Search */}
        <div className="relative mb-6">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search phone numbers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
          />
        </div>

        {loading && filteredPhoneNumbers.length === 0 ? (
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-8 text-center">
            <div className="animate-spin w-6 h-6 border-2 border-gray-600 border-t-white rounded-full mx-auto mb-2"></div>
            <p className="text-gray-400">Loading phone numbers...</p>
          </div>
        ) : filteredPhoneNumbers.length > 0 ? (
          /* Phone numbers table */
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800">
            <div className="grid grid-cols-[1fr,200px,200px,200px,80px] px-4 py-3 border-b border-gray-800">
              <div className="text-sm text-gray-400">Number</div>
              <div className="text-sm text-gray-400">Assistant</div>
              <div className="text-sm text-gray-400">Provider</div>
              <div className="text-sm text-gray-400">Created</div>
              <div className="text-sm text-gray-400">Actions</div>
            </div>

            {filteredPhoneNumbers.map((number) => (
              <div
                key={number.id}
                className="grid grid-cols-[1fr,200px,200px,200px,80px] px-4 py-3 hover:bg-[#252525] transition-colors"
              >
                <div className="text-sm font-medium flex items-center gap-2">
                  <Phone size={16} className="text-gray-400" />
                  {number.number}
                  {number.name && (
                    <span className="px-2 py-0.5 bg-gray-800 rounded-full text-xs text-gray-400">
                      {number.name}
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-400">
                  {number.assistantId ? (
                    assistants.find(a => a.id === number.assistantId)?.name || 'Unknown Assistant'
                  ) : (
                    'No assistant'
                  )}
                </div>
                <div className="text-sm text-gray-400 capitalize">{number.provider}</div>
                <div className="text-sm text-gray-400">{formatDate(number.createdAt)}</div>
                <div className="flex justify-end gap-1">
                  <button
                    className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                    onClick={() => handleEditPhoneNumber(number)}
                    title="Edit phone number"
                  >
                    <Edit size={14} className="text-gray-400" />
                  </button>
                  <button
                    className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                    onClick={() => setShowDeleteConfirm(number.id)}
                    title="Delete phone number"
                  >
                    <Trash2 size={14} className="text-red-400" />
                  </button>
                  <button className="p-1 hover:bg-[#333333] rounded-md transition-colors">
                    <MoreHorizontal size={14} className="text-gray-400" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* Empty state */
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-12 text-center">
            <div className="w-12 h-12 bg-[#252525] rounded-lg flex items-center justify-center mx-auto mb-4">
              <Phone size={24} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {searchQuery ? 'No phone numbers found' : 'No phone numbers'}
            </h3>
            <p className="text-sm text-gray-400 mb-6">
              {searchQuery
                ? 'No phone numbers match your search criteria.'
                : 'You don\'t have any phone numbers in your Vapi account yet.'
              }
            </p>
            {!searchQuery && (
              <button
                onClick={() => setIsImportModalOpen(true)}
                className="px-4 py-2 bg-[#252525] text-white rounded-lg text-sm hover:bg-[#333333] transition-colors inline-flex items-center gap-2"
              >
                <Plus size={16} />
                Add number
              </button>
            )}
          </div>
        )}
      </div>

      {/* Add Phone Number Modal */}
      <Modal
        isOpen={isImportModalOpen}
        onClose={() => {
          setIsImportModalOpen(false);
          setPhoneNumber('');
          setPhoneNumberName('');
          setSelectedAssistant('');
        }}
        title="Add Phone Number"
      >
        <form onSubmit={handleCreatePhoneNumber} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="+****************"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
              required
            />
            <p className="text-xs text-gray-400 mt-1">
              Enter the phone number in international format (e.g., +1234567890)
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Name (Optional)
            </label>
            <input
              type="text"
              value={phoneNumberName}
              onChange={(e) => setPhoneNumberName(e.target.value)}
              placeholder="e.g., Sales Line, Support"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Assistant (Optional)
            </label>
            <select
              value={selectedAssistant}
              onChange={(e) => setSelectedAssistant(e.target.value)}
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm focus:outline-none focus:border-gray-700 transition-colors"
            >
              <option value="">No assistant assigned</option>
              {assistants.map((assistant) => (
                <option key={assistant.id} value={assistant.id}>
                  {assistant.name}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-400 mt-1">
              Choose which assistant will handle calls to this number
            </p>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setIsImportModalOpen(false);
                setPhoneNumber('');
                setPhoneNumberName('');
                setSelectedAssistant('');
              }}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-white text-black text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Adding...' : 'Add Number'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Edit Phone Number Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedPhoneNumber(null);
          setPhoneNumberName('');
          setSelectedAssistant('');
        }}
        title="Edit Phone Number"
      >
        <form onSubmit={handleUpdatePhoneNumber} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              value={selectedPhoneNumber?.number || ''}
              disabled
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm text-gray-500 cursor-not-allowed"
            />
            <p className="text-xs text-gray-400 mt-1">
              Phone number cannot be changed after creation
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Name (Optional)
            </label>
            <input
              type="text"
              value={phoneNumberName}
              onChange={(e) => setPhoneNumberName(e.target.value)}
              placeholder="e.g., Sales Line, Support"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Assistant (Optional)
            </label>
            <select
              value={selectedAssistant}
              onChange={(e) => setSelectedAssistant(e.target.value)}
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm focus:outline-none focus:border-gray-700 transition-colors"
            >
              <option value="">No assistant assigned</option>
              {assistants.map((assistant) => (
                <option key={assistant.id} value={assistant.id}>
                  {assistant.name}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-400 mt-1">
              Choose which assistant will handle calls to this number
            </p>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setIsEditModalOpen(false);
                setSelectedPhoneNumber(null);
                setPhoneNumberName('');
                setSelectedAssistant('');
              }}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-white text-black text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Updating...' : 'Update Number'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(null)}
        title="Delete Phone Number"
      >
        <div className="space-y-4">
          <p className="text-gray-300">
            Are you sure you want to delete this phone number? This action cannot be undone and will remove the number from your Vapi account.
          </p>
          <div className="flex justify-end gap-3">
            <button
              onClick={() => setShowDeleteConfirm(null)}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => showDeleteConfirm && handleDeletePhoneNumber(showDeleteConfirm)}
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};