import { BaseApiService } from '../base.js';
import {
  ApiResponse,
  ErrorResponse,
  PhoneNumberListQuery,
  CreatePhoneNumberRequest,
  Vapi
} from '../types.js';

/**
 * Phone Number Management Service
 * Handles all phone number-related operations including creating, retrieving, updating, and deleting phone numbers
 */
export class PhoneNumbersService extends BaseApiService {

  /**
   * Create/purchase a new phone number
   */
  async createPhoneNumber(request: CreatePhoneNumberRequest): Promise<ApiResponse<Vapi.PhoneNumber> | ErrorResponse> {
    this.logOperation('createPhoneNumber', request);

    return this.handleApiCall(async () => {
      // Validate required fields
      this.validateRequired(request, ['provider', 'credentialId']);

      const phoneNumberData: Vapi.PhoneNumbersCreateRequest = {
        provider: request.provider,
        credentialId: request.credentialId,
        name: request.name,
        assistantId: request.assistantId,
      };

      return await this.client.phoneNumbers.create(phoneNumberData);
    }, 'Phone number created successfully');
  }

  /**
   * List all phone numbers with optional filtering
   */
  async listPhoneNumbers(query: PhoneNumberListQuery = {}): Promise<ApiResponse<Vapi.PhoneNumbersListResponseItem[]> | ErrorResponse> {
    this.logOperation('listPhoneNumbers', query);

    return this.handleApiCall(async () => {
      const requestParams: any = {};

      // Add query parameters if provided
      if (query.provider) requestParams.provider = query.provider;
      if (query.assignedToAssistant !== undefined) {
        requestParams.assignedToAssistant = query.assignedToAssistant;
      }

      return await this.client.phoneNumbers.list(requestParams);
    }, 'Phone numbers retrieved successfully');
  }

  /**
   * Get a specific phone number by ID
   */
  async getPhoneNumber(id: string): Promise<ApiResponse<Vapi.PhoneNumber> | ErrorResponse> {
    this.logOperation('getPhoneNumber', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'phone number ID');
      return await this.client.phoneNumbers.get(validId);
    }, 'Phone number retrieved successfully');
  }

  /**
   * Update an existing phone number
   */
  async updatePhoneNumber(
    id: string,
    updates: Vapi.PhoneNumbersUpdateRequest
  ): Promise<ApiResponse<Vapi.PhoneNumber> | ErrorResponse> {
    this.logOperation('updatePhoneNumber', { id, updates });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'phone number ID');
      return await this.client.phoneNumbers.update(validId, updates);
    }, 'Phone number updated successfully');
  }

  /**
   * Delete a phone number
   */
  async deletePhoneNumber(id: string): Promise<ApiResponse<Vapi.PhoneNumber> | ErrorResponse> {
    this.logOperation('deletePhoneNumber', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'phone number ID');
      return await this.client.phoneNumbers.delete(validId);
    }, 'Phone number deleted successfully');
  }

  /**
   * Assign an assistant to a phone number
   */
  async assignAssistant(
    phoneNumberId: string,
    assistantId: string
  ): Promise<ApiResponse<Vapi.PhoneNumber> | ErrorResponse> {
    this.logOperation('assignAssistant', { phoneNumberId, assistantId });

    return this.handleApiCall(async () => {
      const validPhoneNumberId = this.validateId(phoneNumberId, 'phone number ID');
      const validAssistantId = this.validateId(assistantId, 'assistant ID');

      return await this.client.phoneNumbers.update(validPhoneNumberId, {
        assistantId: validAssistantId,
      });
    }, 'Assistant assigned to phone number successfully');
  }

  /**
   * Unassign assistant from a phone number
   */
  async unassignAssistant(phoneNumberId: string): Promise<ApiResponse<Vapi.PhoneNumber> | ErrorResponse> {
    this.logOperation('unassignAssistant', { phoneNumberId });

    return this.handleApiCall(async () => {
      const validPhoneNumberId = this.validateId(phoneNumberId, 'phone number ID');

      return await this.client.phoneNumbers.update(validPhoneNumberId, {
        assistantId: null,
      });
    }, 'Assistant unassigned from phone number successfully');
  }

  /**
   * Get phone number usage statistics
   */
  async getPhoneNumberUsage(
    id: string,
    dateRange?: { start: string; end: string }
  ): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getPhoneNumberUsage', { id, dateRange });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'phone number ID');

      // Get phone number details
      const phoneNumber = await this.client.phoneNumbers.get(validId);

      // Build query parameters for date range
      const queryParams: any = { phoneNumberId: validId };
      if (dateRange?.start) queryParams.createdAtGte = dateRange.start;
      if (dateRange?.end) queryParams.createdAtLte = dateRange.end;

      // Get calls for this phone number within the date range
      const calls = await this.client.calls.list(queryParams);

      // Calculate usage statistics
      const totalMinutes = calls.reduce((sum, call) => {
        if (call.endedAt && call.startedAt) {
          const duration = new Date(call.endedAt).getTime() - new Date(call.startedAt).getTime();
          return sum + (duration / (1000 * 60)); // Convert to minutes
        }
        return sum;
      }, 0);

      const totalCost = calls.reduce((sum, call) => sum + (call.cost || 0), 0);
      const inboundCalls = calls.filter(call => call.type === 'inbound').length;
      const outboundCalls = calls.filter(call => call.type === 'outbound').length;

      return {
        phoneNumberId: validId,
        phoneNumber: phoneNumber.number,
        dateRange: dateRange || { start: 'all-time', end: 'now' },
        usage: {
          totalCalls: calls.length,
          inboundCalls,
          outboundCalls,
          totalMinutes: Math.round(totalMinutes * 100) / 100,
          totalCost: Math.round(totalCost * 100) / 100,
          averageCallDuration: calls.length > 0 ? totalMinutes / calls.length : 0,
        },
        lastUpdated: new Date().toISOString(),
      };
    }, 'Phone number usage statistics retrieved successfully');
  }

  /**
   * Get available phone numbers for purchase
   */
  async getAvailableNumbers(
    areaCode?: string,
    country?: string
  ): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getAvailableNumbers', { areaCode, country });

    return this.handleApiCall(async () => {
      // This would typically query available numbers from the provider
      // Implementation depends on Vapi's available number search capabilities

      return {
        availableNumbers: [],
        searchCriteria: {
          areaCode: areaCode || 'any',
          country: country || 'US',
        },
        message: 'Available numbers search - implementation depends on provider capabilities',
      };
    }, 'Available numbers retrieved successfully');
  }

  /**
   * Configure phone number settings
   */
  async configurePhoneNumber(
    id: string,
    config: {
      webhookUrl?: string;
      forwardingNumber?: string;
      recordCalls?: boolean;
      transcribeVoicemail?: boolean;
    }
  ): Promise<ApiResponse<Vapi.PhoneNumber> | ErrorResponse> {
    this.logOperation('configurePhoneNumber', { id, config });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'phone number ID');

      // Update phone number with configuration
      return await this.client.phoneNumbers.update(validId, {
        ...config,
      });
    }, 'Phone number configured successfully');
  }
}
