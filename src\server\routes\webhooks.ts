import { Router, Request, Response } from 'express';
import { WebhookEvent } from '../../api/types.js';

const router = Router();

/**
 * POST /api/webhooks/vapi
 * Handle Vapi event stream (function-call, assistant-request, etc.)
 */
router.post('/vapi', async (req: Request, res: Response) => {
  try {
    const event: WebhookEvent = {
      type: req.body.type || 'unknown',
      data: req.body,
      timestamp: new Date().toISOString(),
      callId: req.body.callId,
      assistantId: req.body.assistantId,
    };

    console.log('Received Vapi webhook:', event);

    // Process different event types
    switch (event.type) {
      case 'function-call':
        await handleFunctionCall(event);
        break;
      case 'assistant-request':
        await handleAssistantRequest(event);
        break;
      case 'call-start':
        await handleCallStart(event);
        break;
      case 'call-end':
        await handleCallEnd(event);
        break;
      case 'transcript':
        await handleTranscript(event);
        break;
      case 'hang':
        await handleHang(event);
        break;
      case 'speech-start':
        await handleSpeechStart(event);
        break;
      case 'speech-end':
        await handleSpeechEnd(event);
        break;
      default:
        console.log('Unknown webhook event type:', event.type);
    }

    res.status(200).json({
      success: true,
      message: 'Webhook processed successfully',
      eventType: event.type,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error processing Vapi webhook:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process webhook',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/webhooks/completions
 * Handle SSE-style LLM completions or tool triggers
 */
router.post('/completions', async (req: Request, res: Response) => {
  try {
    const completionData = req.body;

    console.log('Received completion webhook:', completionData);

    // Process completion data
    // This could be used for real-time updates, logging, or triggering other actions

    res.status(200).json({
      success: true,
      message: 'Completion webhook processed successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error processing completion webhook:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process completion webhook',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/webhooks/call-events
 * General fallback for transcription, callEnd, etc.
 */
router.post('/call-events', async (req: Request, res: Response) => {
  try {
    const eventData = req.body;

    console.log('Received call event webhook:', eventData);

    // Process call events
    // This could include transcription updates, call status changes, etc.

    res.status(200).json({
      success: true,
      message: 'Call event webhook processed successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error processing call event webhook:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process call event webhook',
      timestamp: new Date().toISOString(),
    });
  }
});

// Webhook event handlers

async function handleFunctionCall(event: WebhookEvent): Promise<void> {
  console.log('Processing function call:', event.data);

  // Handle function calls from the assistant
  // This could trigger external APIs, database operations, etc.

  const functionName = event.data.functionCall?.name;
  const parameters = event.data.functionCall?.parameters;

  switch (functionName) {
    case 'get_weather':
      // Handle weather function call
      break;
    case 'book_appointment':
      // Handle appointment booking
      break;
    case 'transfer_call':
      // Handle call transfer
      break;
    default:
      console.log('Unknown function call:', functionName);
  }
}

async function handleAssistantRequest(event: WebhookEvent): Promise<void> {
  console.log('Processing assistant request:', event.data);

  // Handle requests for assistant configuration or updates
  // This could be used for dynamic assistant behavior
}

async function handleCallStart(event: WebhookEvent): Promise<void> {
  console.log('Call started:', event.data);

  // Log call start, update analytics, notify systems, etc.
  // You could store this in a database for tracking
}

async function handleCallEnd(event: WebhookEvent): Promise<void> {
  console.log('Call ended:', event.data);

  // Process call completion
  // Update analytics, send notifications, trigger follow-up actions, etc.

  const callData = event.data;
  const duration = callData.endedAt && callData.startedAt
    ? new Date(callData.endedAt).getTime() - new Date(callData.startedAt).getTime()
    : 0;

  console.log(`Call ${event.callId} ended after ${duration}ms`);
}

async function handleTranscript(event: WebhookEvent): Promise<void> {
  console.log('Transcript update:', event.data);

  // Process real-time transcript updates
  // This could be used for live monitoring, sentiment analysis, etc.

  const transcript = event.data.transcript;
  const role = event.data.role; // 'user' or 'assistant'

  // Store transcript, analyze sentiment, trigger alerts, etc.
}

async function handleHang(event: WebhookEvent): Promise<void> {
  console.log('Call hang detected:', event.data);

  // Handle when someone hangs up
  // Update call status, trigger cleanup, etc.
}

async function handleSpeechStart(event: WebhookEvent): Promise<void> {
  console.log('Speech started:', event.data);

  // Handle when speech detection starts
  // Could be used for real-time monitoring
}

async function handleSpeechEnd(event: WebhookEvent): Promise<void> {
  console.log('Speech ended:', event.data);

  // Handle when speech detection ends
  // Could be used for real-time monitoring
}

export default router;
