// Import all API services
import { CallsService } from './calls.js';
import { AssistantsService } from './assistants.js';
import { PhoneNumbersService } from './phoneNumbers.js';
import { TemplatesService } from './templates.js';
import { AnalyticsService } from './analytics.js';

// Export all API services
export { CallsService } from './calls.js';
export { AssistantsService } from './assistants.js';
export { PhoneNumbersService } from './phoneNumbers.js';
export { TemplatesService } from './templates.js';
export { AnalyticsService } from './analytics.js';

// Export base service for extending
export { BaseApiService } from '../base.js';

// Export configuration and types
export { default as VapiConfig } from '../config.js';
export * from '../types.js';

/**
 * Main API service factory
 * Creates and manages all Vapi API service instances
 */
export class VapiApiServices {
  private _calls?: CallsService;
  private _assistants?: AssistantsService;
  private _phoneNumbers?: PhoneNumbersService;
  private _templates?: TemplatesService;
  private _analytics?: AnalyticsService;

  constructor() {
    // Services will be initialized lazily
  }

  get calls(): CallsService {
    if (!this._calls) {
      this._calls = new CallsService();
    }
    return this._calls;
  }

  get assistants(): AssistantsService {
    if (!this._assistants) {
      this._assistants = new AssistantsService();
    }
    return this._assistants;
  }

  get phoneNumbers(): PhoneNumbersService {
    if (!this._phoneNumbers) {
      this._phoneNumbers = new PhoneNumbersService();
    }
    return this._phoneNumbers;
  }

  get templates(): TemplatesService {
    if (!this._templates) {
      this._templates = new TemplatesService();
    }
    return this._templates;
  }

  get analytics(): AnalyticsService {
    if (!this._analytics) {
      this._analytics = new AnalyticsService();
    }
    return this._analytics;
  }

  /**
   * Health check for all services
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    services: Record<string, boolean>;
    timestamp: string;
  }> {
    const results = {
      calls: false,
      assistants: false,
      phoneNumbers: false,
      templates: true, // Templates service is always healthy (in-memory)
      analytics: false,
    };

    try {
      // Test calls service
      await this.calls.listCalls({ limit: 1 });
      results.calls = true;
    } catch (error) {
      console.error('Calls service health check failed:', error);
    }

    try {
      // Test assistants service
      await this.assistants.listAssistants({ limit: 1 });
      results.assistants = true;
    } catch (error) {
      console.error('Assistants service health check failed:', error);
    }

    try {
      // Test phone numbers service
      await this.phoneNumbers.listPhoneNumbers({ limit: 1 });
      results.phoneNumbers = true;
    } catch (error) {
      console.error('Phone numbers service health check failed:', error);
    }

    try {
      // Test analytics service with a simple query
      await this.analytics.getDashboardData();
      results.analytics = true;
    } catch (error) {
      console.error('Analytics service health check failed:', error);
    }

    const allHealthy = Object.values(results).every(status => status === true);

    return {
      status: allHealthy ? 'healthy' : 'unhealthy',
      services: results,
      timestamp: new Date().toISOString(),
    };
  }
}

// Create singleton instance
export const vapiServices = new VapiApiServices();
