import React from 'react';
import { ChevronDown } from 'lucide-react';

interface UserProfileProps {
  name: string;
  workspace: string;
  avatarText: string;
  isCollapsed?: boolean;
}

export const UserProfile: React.FC<UserProfileProps> = ({ 
  name, 
  workspace, 
  avatarText,
  isCollapsed = false
}) => {
  return (
    <div className={`${isCollapsed ? 'justify-center' : 'px-4'} py-3 flex items-center cursor-pointer hover:bg-gray-800 transition-colors duration-200`}>
      <div className="w-8 h-8 rounded-full bg-purple-600 flex items-center justify-center text-white mr-3">
        {avatarText}
      </div>
      {!isCollapsed && (
        <>
        <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate">{name}</div>
        <div className="text-xs text-gray-400 truncate">{workspace}</div>
        </div>
        <ChevronDown size={16} className="text-gray-500" />
        </>
      )}
    </div>
  );
};