{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["src/server/**/*", "src/api/**/*"], "exclude": ["node_modules", "dist", "src/components", "src/pages", "src/context", "src/main.tsx", "src/App.tsx", "**/*.test.ts", "**/*.test.tsx"]}