import { BaseApiService } from '../base.js';
import {
  ApiResponse,
  ErrorResponse,
  CreateCallRequest,
  CallListQuery,
  Vapi
} from '../types.js';

/**
 * Call Management Service
 * Handles all call-related operations including creating, retrieving, updating, and deleting calls
 */
export class CallsService extends BaseApiService {

  /**
   * Create a new outbound call
   */
  async createCall(request: CreateCallRequest): Promise<ApiResponse<Vapi.Call> | ErrorResponse> {
    this.logOperation('createCall', request);

    return this.handleApiCall(async () => {
      // Validate required fields based on call type
      if (!request.assistantId && !request.assistant) {
        throw new Error('Either assistantId or assistant configuration is required');
      }

      const callData: Vapi.CreateCallDto = {
        assistantId: request.assistantId,
        assistant: request.assistant,
        phoneNumberId: request.phoneNumberId,
        customer: request.customer,
        metadata: request.metadata,
      };

      return await this.client.calls.create(callData);
    }, 'Call created successfully');
  }

  /**
   * List all calls with optional filtering
   */
  async listCalls(query: CallListQuery = {}): Promise<ApiResponse<Vapi.Call[]> | ErrorResponse> {
    this.logOperation('listCalls', query);

    return this.handleApiCall(async () => {
      const requestParams: any = {};

      // Add query parameters if provided
      if (query.assistantId) requestParams.assistantId = query.assistantId;
      if (query.phoneNumberId) requestParams.phoneNumberId = query.phoneNumberId;
      if (query.status) requestParams.status = query.status;
      if (query.createdAtGte) requestParams.createdAtGte = query.createdAtGte;
      if (query.createdAtLte) requestParams.createdAtLte = query.createdAtLte;

      return await this.client.calls.list(requestParams);
    }, 'Calls retrieved successfully');
  }

  /**
   * Get a specific call by ID
   */
  async getCall(id: string): Promise<ApiResponse<Vapi.Call> | ErrorResponse> {
    this.logOperation('getCall', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'call ID');
      return await this.client.calls.get(validId);
    }, 'Call retrieved successfully');
  }

  /**
   * Update an existing call
   */
  async updateCall(
    id: string,
    updates: Vapi.UpdateCallDto
  ): Promise<ApiResponse<Vapi.Call> | ErrorResponse> {
    this.logOperation('updateCall', { id, updates });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'call ID');
      return await this.client.calls.update(validId, updates);
    }, 'Call updated successfully');
  }

  /**
   * Delete a call
   */
  async deleteCall(id: string): Promise<ApiResponse<Vapi.Call> | ErrorResponse> {
    this.logOperation('deleteCall', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'call ID');
      return await this.client.calls.delete(validId);
    }, 'Call deleted successfully');
  }

  /**
   * End a live call early
   */
  async endCall(id: string): Promise<ApiResponse<Vapi.Call> | ErrorResponse> {
    this.logOperation('endCall', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'call ID');

      // End the call by updating its status
      return await this.client.calls.update(validId, {
        // Add appropriate fields to end the call
        // This might need adjustment based on Vapi's actual API
      });
    }, 'Call ended successfully');
  }

  /**
   * Get call transcript
   */
  async getCallTranscript(id: string): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getCallTranscript', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'call ID');
      const call = await this.client.calls.get(validId);

      // Extract transcript from call data
      // This might need adjustment based on Vapi's actual response structure
      return {
        callId: id,
        transcript: call.transcript || null,
        messages: call.messages || [],
      };
    }, 'Call transcript retrieved successfully');
  }

  /**
   * Get call recording/audio
   */
  async getCallAudio(id: string): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getCallAudio', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'call ID');
      const call = await this.client.calls.get(validId);

      // Extract audio/recording information from call data
      // This might need adjustment based on Vapi's actual response structure
      return {
        callId: id,
        recordingUrl: call.recordingUrl || null,
        audioData: call.audioData || null,
      };
    }, 'Call audio retrieved successfully');
  }

  /**
   * Get call analytics/stats
   */
  async getCallStats(id: string): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getCallStats', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'call ID');
      const call = await this.client.calls.get(validId);

      // Extract analytics from call data
      return {
        callId: id,
        duration: call.endedAt && call.startedAt
          ? new Date(call.endedAt).getTime() - new Date(call.startedAt).getTime()
          : null,
        status: call.status,
        cost: call.cost || null,
        startedAt: call.startedAt,
        endedAt: call.endedAt,
        assistantId: call.assistantId,
        phoneNumberId: call.phoneNumberId,
      };
    }, 'Call stats retrieved successfully');
  }
}
