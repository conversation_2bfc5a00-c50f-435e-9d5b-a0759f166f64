import React, { useState } from 'react';
import { apiClient } from '../services/api';
import { AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';

export const VapiDebug: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testVapiConnection = async () => {
    setTesting(true);
    setError(null);
    setResults(null);

    try {
      console.log('Testing Vapi API connection...');
      
      // Test 1: Health check
      console.log('1. Testing health check...');
      const healthResult = await apiClient.healthCheck();
      console.log('Health check result:', healthResult);

      // Test 2: Get assistants
      console.log('2. Testing get assistants...');
      const assistantsResult = await apiClient.getAssistants();
      console.log('Assistants result:', assistantsResult);

      // Test 3: Get templates
      console.log('3. Testing get templates...');
      const templatesResult = await apiClient.getTemplates();
      console.log('Templates result:', templatesResult);

      setResults({
        health: healthResult,
        assistants: assistantsResult,
        templates: templatesResult,
      });

    } catch (err) {
      console.error('Vapi test failed:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-[#1A1A1A] rounded-lg border border-gray-800">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white">Vapi API Debug</h2>
        <button
          onClick={testVapiConnection}
          disabled={testing}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <RefreshCw size={16} className={testing ? 'animate-spin' : ''} />
          {testing ? 'Testing...' : 'Test Connection'}
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-900/20 border border-red-800 rounded-lg flex items-center gap-2">
          <AlertCircle size={18} className="text-red-400" />
          <div>
            <p className="text-red-400 font-medium">Connection Failed</p>
            <p className="text-red-300 text-sm">{error}</p>
          </div>
        </div>
      )}

      {results && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <CheckCircle size={18} className="text-green-400" />
            <p className="text-green-400 font-medium">Connection Successful!</p>
          </div>

          {/* Health Check Results */}
          <div className="bg-[#0F0F0F] rounded-lg p-4 border border-gray-800">
            <h3 className="text-sm font-medium text-white mb-2">Health Check</h3>
            <pre className="text-xs text-gray-300 overflow-x-auto">
              {JSON.stringify(results.health, null, 2)}
            </pre>
          </div>

          {/* Assistants Results */}
          <div className="bg-[#0F0F0F] rounded-lg p-4 border border-gray-800">
            <h3 className="text-sm font-medium text-white mb-2">
              Assistants ({results.assistants?.data?.length || 0} found)
            </h3>
            <pre className="text-xs text-gray-300 overflow-x-auto max-h-40 overflow-y-auto">
              {JSON.stringify(results.assistants, null, 2)}
            </pre>
          </div>

          {/* Templates Results */}
          <div className="bg-[#0F0F0F] rounded-lg p-4 border border-gray-800">
            <h3 className="text-sm font-medium text-white mb-2">
              Templates ({results.templates?.data?.length || 0} found)
            </h3>
            <pre className="text-xs text-gray-300 overflow-x-auto max-h-40 overflow-y-auto">
              {JSON.stringify(results.templates, null, 2)}
            </pre>
          </div>
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-900/20 border border-blue-800 rounded-lg">
        <p className="text-blue-400 text-sm">
          <strong>Debug Info:</strong> Check the browser console for detailed request/response logs.
          This will help identify any API connection issues.
        </p>
      </div>
    </div>
  );
};
