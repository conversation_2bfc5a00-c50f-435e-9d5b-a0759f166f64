import { useState, useEffect, useCallback } from 'react';
import { apiClient, VapiFile } from '../services/api';

export const useFiles = () => {
  const [files, setFiles] = useState<VapiFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);

  const fetchFiles = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getFiles();
      if (response.success && response.data) {
        // Sort files by creation date (newest first)
        const sortedFiles = response.data.sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        setFiles(sortedFiles);
      } else {
        setError(response.error || 'Failed to fetch files');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch files');
    } finally {
      setLoading(false);
    }
  }, []);

  const getFile = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getFile(id);
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get file');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get file';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const uploadFile = useCallback(async (file: File) => {
    try {
      setLoading(true);
      setError(null);
      setUploadProgress(0);

      // Simulate upload progress (since we can't track real progress with current API)
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      const response = await apiClient.uploadFile(file);
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.success && response.data) {
        setFiles(prev => [response.data!, ...prev]);
        setTimeout(() => setUploadProgress(0), 1000); // Reset progress after 1 second
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to upload file');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload file';
      setError(errorMessage);
      setUploadProgress(0);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteFile = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.deleteFile(id);
      if (response.success) {
        setFiles(prev => prev.filter(file => file.id !== id));
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete file');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete file';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Helper functions
  const getFilesByType = useCallback((mimeType: string) => {
    return files.filter(file => file.mimetype.startsWith(mimeType));
  }, [files]);

  const getDocumentFiles = useCallback(() => {
    const documentTypes = ['application/pdf', 'text/', 'application/msword', 'application/vnd.openxmlformats'];
    return files.filter(file => 
      documentTypes.some(type => file.mimetype.startsWith(type))
    );
  }, [files]);

  const getImageFiles = useCallback(() => {
    return files.filter(file => file.mimetype.startsWith('image/'));
  }, [files]);

  const formatFileSize = useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  const getFileIcon = useCallback((mimeType: string) => {
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return '📊';
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return '📽️';
    if (mimeType.startsWith('text/')) return '📃';
    if (mimeType.startsWith('audio/')) return '🎵';
    if (mimeType.startsWith('video/')) return '🎬';
    return '📁';
  }, []);

  const isValidFileType = useCallback((file: File) => {
    // Common supported file types for knowledge bases
    const supportedTypes = [
      'application/pdf',
      'text/plain',
      'text/markdown',
      'text/csv',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/json',
      'text/html',
    ];
    
    return supportedTypes.some(type => file.type.startsWith(type.split('/')[0]) || file.type === type);
  }, []);

  const getFileTypeColor = useCallback((mimeType: string) => {
    if (mimeType.startsWith('image/')) return 'bg-green-500/20 text-green-400';
    if (mimeType.includes('pdf')) return 'bg-red-500/20 text-red-400';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'bg-blue-500/20 text-blue-400';
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return 'bg-emerald-500/20 text-emerald-400';
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'bg-orange-500/20 text-orange-400';
    if (mimeType.startsWith('text/')) return 'bg-gray-500/20 text-gray-400';
    if (mimeType.startsWith('audio/')) return 'bg-purple-500/20 text-purple-400';
    if (mimeType.startsWith('video/')) return 'bg-pink-500/20 text-pink-400';
    return 'bg-gray-500/20 text-gray-400';
  }, []);

  useEffect(() => {
    fetchFiles();
  }, [fetchFiles]);

  return {
    files,
    loading,
    error,
    uploadProgress,
    fetchFiles,
    getFile,
    uploadFile,
    deleteFile,
    getFilesByType,
    getDocumentFiles,
    getImageFiles,
    formatFileSize,
    getFileIcon,
    getFileTypeColor,
    isValidFileType,
    clearError: () => setError(null),
  };
};
