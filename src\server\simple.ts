import express from 'express';
import cors from 'cors';
import { config } from '../api/config.js';

async function createSimpleApp() {
  const app = express();

  // Basic middleware
  app.use(cors());
  app.use(express.json());

  // Root endpoint
  app.get('/', (req, res) => {
    res.json({
      message: 'Vapi API Wrapper Server',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/api/health',
        calls: '/api/calls',
        assistants: '/api/assistants',
        phoneNumbers: '/api/phone-numbers',
        templates: '/api/templates',
        analytics: '/api/analytics',
        webhooks: '/api/webhooks',
      },
    });
  });

  // Load routes
  const healthRouter = (await import('./routes/health.js')).default;
  app.use('/api/health', healthRouter);

  const callsRouter = (await import('./routes/calls.js')).default;
  app.use('/api/calls', callsRouter);

  const assistantsRouter = (await import('./routes/assistants.js')).default;
  app.use('/api/assistants', assistantsRouter);

  const phoneNumbersRouter = (await import('./routes/phoneNumbers.js')).default;
  app.use('/api/phone-numbers', phoneNumbersRouter);

  const templatesRouter = (await import('./routes/templates.js')).default;
  app.use('/api/templates', templatesRouter);

  const analyticsRouter = (await import('./routes/analytics.js')).default;
  app.use('/api/analytics', analyticsRouter);

  const webhooksRouter = (await import('./routes/webhooks.js')).default;
  app.use('/api/webhooks', webhooksRouter);

  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      success: false,
      error: 'Endpoint not found',
      message: `The endpoint ${req.method} ${req.originalUrl} does not exist`,
      timestamp: new Date().toISOString(),
    });
  });

  return app;
}

async function startSimpleServer() {
  try {
    const app = await createSimpleApp();
    const port = config.server.port;

    app.listen(port, () => {
      console.log(`🚀 Vapi API Wrapper Server running on port ${port}`);
      console.log(`📊 Environment: ${config.server.nodeEnv}`);
      console.log(`🔗 Health check: http://localhost:${port}/api/health`);
      console.log(`📚 API docs: http://localhost:${port}/`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startSimpleServer();
