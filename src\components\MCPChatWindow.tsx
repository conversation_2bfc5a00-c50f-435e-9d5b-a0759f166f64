import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader, Minimize2, Maximize2, X, Settings } from 'lucide-react';
import { mcpAgent } from '../agents/langgraph-mcp-agent';
import { MCPAgentConfig } from './MCPAgentConfig';

interface Message {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

interface MCPChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize: () => void;
  isMinimized: boolean;
}

export const MCPChatWindow: React.FC<MCPChatWindowProps> = ({
  isOpen,
  onClose,
  onMinimize,
  isMinimized,
}) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'agent',
      content: `Hello! I'm your LangGraph MCP agent powered by OpenRouter! 🤖✨

I have access to powerful MCP tools and can help you with:

🔧 **Code Modifications**
- Create new files and components
- Edit existing code
- Update specific functions
- View file contents

📞 **Vapi Integrations**
- Create tools (Google Calendar, Slack, GHL, etc.)
- Manage assistants and knowledge bases
- Handle call operations
- Configure voice AI workflows

🎨 **UI Components**
- Create React components
- Update styling with Tailwind
- Modify pages and layouts
- Build custom hooks

**Try asking me:**
- "Create a new modal component for user settings"
- "Add a Google Calendar tool to the Tools page"
- "List all Vapi assistants"
- "Create a knowledge base with product docs"
- "Update the sidebar navigation"
- "Help me build a new feature"

I'm ready to help you build amazing voice AI experiences! What would you like to work on?`,
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && !isMinimized) {
      inputRef.current?.focus();
    }
  }, [isOpen, isMinimized]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Use the actual LangGraph MCP agent
      console.log('🤖 Sending message to MCP agent:', inputValue);
      const agentResponse = await mcpAgent.chat(inputValue);

      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: agentResponse,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, agentMessage]);
      console.log('✅ Received response from MCP agent:', agentResponse);
    } catch (error) {
      console.error('❌ MCP Agent error:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: `Sorry, I encountered an error processing your request: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed bottom-4 right-4 bg-[#1A1A1A] border border-gray-800 rounded-lg shadow-2xl z-50 transition-all duration-300 ${
      isMinimized ? 'w-80 h-12' : 'w-96 h-[600px]'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-800">
        <div className="flex items-center gap-2">
          <Bot size={18} className="text-blue-400" />
          <span className="text-sm font-medium text-white">MCP Agent</span>
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        </div>
        <div className="flex items-center gap-1">
          <button
            onClick={() => setIsConfigModalOpen(true)}
            className="p-1 hover:bg-gray-800 rounded transition-colors"
            title="Configure Agent"
          >
            <Settings size={14} className="text-gray-400" />
          </button>
          <button
            onClick={onMinimize}
            className="p-1 hover:bg-gray-800 rounded transition-colors"
            title={isMinimized ? "Maximize" : "Minimize"}
          >
            {isMinimized ? (
              <Maximize2 size={14} className="text-gray-400" />
            ) : (
              <Minimize2 size={14} className="text-gray-400" />
            )}
          </button>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-800 rounded transition-colors"
            title="Close"
          >
            <X size={14} className="text-gray-400" />
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 h-[480px]">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.type === 'agent' && (
                  <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Bot size={16} className="text-blue-400" />
                  </div>
                )}
                <div
                  className={`max-w-[80%] p-3 rounded-lg text-sm ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-[#0F0F0F] text-gray-200 border border-gray-800'
                  }`}
                >
                  <div className="whitespace-pre-wrap">{message.content}</div>
                  <div className="text-xs opacity-60 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
                {message.type === 'user' && (
                  <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <User size={16} className="text-gray-300" />
                  </div>
                )}
              </div>
            ))}

            {isLoading && (
              <div className="flex gap-3 justify-start">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <Bot size={16} className="text-blue-400" />
                </div>
                <div className="bg-[#0F0F0F] border border-gray-800 p-3 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Loader size={16} className="text-blue-400 animate-spin" />
                    <span className="text-sm text-gray-400">Agent is thinking...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-800">
            <div className="flex gap-2">
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me to modify code, create tools, or update UI..."
                className="flex-1 bg-[#0F0F0F] border border-gray-800 rounded-lg px-3 py-2 text-sm text-white placeholder-gray-500 focus:outline-none focus:border-gray-700 transition-colors"
                disabled={isLoading}
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Send size={16} />
              </button>
            </div>

          </div>
        </>
      )}

      {/* Configuration Modal */}
      <MCPAgentConfig
        isOpen={isConfigModalOpen}
        onClose={() => setIsConfigModalOpen(false)}
      />
    </div>
  );
};
