import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { config } from '../api/config.js';

/**
 * Express application setup with Vapi API wrapper
 */
export async function createApp(): Promise<express.Application> {
  const app = express();

  // Security middleware (simplified to avoid potential issues)
  app.use(helmet());

  // CORS configuration
  app.use(cors({
    origin: config.cors.origin,
    credentials: config.cors.credentials,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));

  // Logging middleware (temporarily disabled for debugging)
  // if (config.server.nodeEnv === 'development') {
  //   app.use(morgan('dev'));
  // } else {
  //   app.use(morgan('combined'));
  // }

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Load routes dynamically to avoid import issues
  try {
    const healthRouter = (await import('./routes/health.js')).default;
    app.use('/api/health', healthRouter);
    console.log('✓ Health routes loaded');
  } catch (error) {
    console.error('✗ Error loading health routes:', error);
  }

  try {
    const callsRouter = (await import('./routes/calls.js')).default;
    app.use('/api/calls', callsRouter);
    console.log('✓ Calls routes loaded');
  } catch (error) {
    console.error('✗ Error loading calls routes:', error);
  }

  try {
    const assistantsRouter = (await import('./routes/assistants.js')).default;
    app.use('/api/assistants', assistantsRouter);
    console.log('✓ Assistants routes loaded');
  } catch (error) {
    console.error('✗ Error loading assistants routes:', error);
  }

  try {
    const phoneNumbersRouter = (await import('./routes/phoneNumbers.js')).default;
    app.use('/api/phone-numbers', phoneNumbersRouter);
    console.log('✓ Phone numbers routes loaded');
  } catch (error) {
    console.error('✗ Error loading phone numbers routes:', error);
  }

  try {
    const templatesRouter = (await import('./routes/templates.js')).default;
    app.use('/api/templates', templatesRouter);
    console.log('✓ Templates routes loaded');
  } catch (error) {
    console.error('✗ Error loading templates routes:', error);
  }

  try {
    const analyticsRouter = (await import('./routes/analytics.js')).default;
    app.use('/api/analytics', analyticsRouter);
    console.log('✓ Analytics routes loaded');
  } catch (error) {
    console.error('✗ Error loading analytics routes:', error);
  }

  try {
    const webhooksRouter = (await import('./routes/webhooks.js')).default;
    app.use('/api/webhooks', webhooksRouter);
    console.log('✓ Webhooks routes loaded');
  } catch (error) {
    console.error('✗ Error loading webhooks routes:', error);
  }

  // Root endpoint
  app.get('/', (req, res) => {
    res.json({
      message: 'Vapi API Wrapper Server',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/api/health',
        calls: '/api/calls',
        assistants: '/api/assistants',
        phoneNumbers: '/api/phone-numbers',
        templates: '/api/templates',
        analytics: '/api/analytics',
        webhooks: '/api/webhooks',
      },
    });
  });

  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      success: false,
      error: 'Endpoint not found',
      message: `The endpoint ${req.method} ${req.originalUrl} does not exist`,
      timestamp: new Date().toISOString(),
    });
  });

  // Global error handler
  app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
    console.error('Global error handler:', err);

    // Don't leak error details in production
    const isDevelopment = config.server.nodeEnv === 'development';

    res.status(err.status || 500).json({
      success: false,
      error: isDevelopment ? err.message : 'Internal server error',
      ...(isDevelopment && { stack: err.stack }),
      timestamp: new Date().toISOString(),
    });
  });

  return app;
}

/**
 * Start the server
 */
export async function startServer(): Promise<void> {
  const app = await createApp();
  const port = config.server.port;

  app.listen(port, () => {
    console.log(`🚀 Vapi API Wrapper Server running on port ${port}`);
    console.log(`📊 Environment: ${config.server.nodeEnv}`);
    console.log(`🔗 Health check: http://localhost:${port}/api/health`);
    console.log(`📚 API docs: http://localhost:${port}/`);
  });
}

export default createApp;
