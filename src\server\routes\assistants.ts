import { Router, Request, Response } from 'express';
import { vapiServices } from '../../api/services/index.js';
import { AssistantListQuery, CreateAssistantRequest, UpdateAssistantRequest } from '../../api/types.js';

const router = Router();

/**
 * GET /api/assistants
 * List available assistants
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const query: AssistantListQuery = {
      page: req.query.page ? parseInt(req.query.page as string) : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      name: req.query.name as string,
      createdAtGte: req.query.createdAtGte as string,
      createdAtLte: req.query.createdAtLte as string,
    };

    const result = await vapiServices.assistants.listAssistants(query);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error listing assistants:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to list assistants',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/assistants
 * Create new assistant
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const assistantRequest: CreateAssistantRequest = req.body;
    const result = await vapiServices.assistants.createAssistant(assistantRequest);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    console.error('Error creating assistant:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create assistant',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/assistants/:id
 * Get full assistant config
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.assistants.getAssistant(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting assistant:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get assistant',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * PATCH /api/assistants/:id
 * Update assistant config
 */
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updates: UpdateAssistantRequest = req.body;
    const result = await vapiServices.assistants.updateAssistant(id, updates);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error updating assistant:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update assistant',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * DELETE /api/assistants/:id
 * Delete assistant
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.assistants.deleteAssistant(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error deleting assistant:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete assistant',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/assistants/:id/clone
 * Clone an existing assistant
 */
router.post('/:id/clone', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name } = req.body;
    const result = await vapiServices.assistants.cloneAssistant(id, name);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    console.error('Error cloning assistant:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clone assistant',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/assistants/:id/metrics
 * Get assistant performance metrics
 */
router.get('/:id/metrics', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.assistants.getAssistantMetrics(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting assistant metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get assistant metrics',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/assistants/:id/test
 * Test an assistant configuration
 */
router.post('/:id/test', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { testMessage } = req.body;
    const result = await vapiServices.assistants.testAssistant(id, testMessage);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error testing assistant:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test assistant',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/assistants/:id/usage
 * Get assistant usage statistics
 */
router.get('/:id/usage', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const dateRange = req.query.start && req.query.end ? {
      start: req.query.start as string,
      end: req.query.end as string,
    } : undefined;

    const result = await vapiServices.assistants.getAssistantUsage(id, dateRange);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting assistant usage:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get assistant usage',
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
