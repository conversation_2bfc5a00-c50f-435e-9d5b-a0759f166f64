import { useRef, useEffect } from 'react';
import * as THREE from 'three';

interface OrbVisualizationProps {
  isListening: boolean;
  isSpeaking?: boolean;
  isCallActive?: boolean;
}

// Beautiful color palettes inspired by your original
const palette = [
  new THREE.Color('#0B0B3B'), // Deep Blue
  new THREE.Color('#3A0CA3'), // Deep Purple
  new THREE.Color('#480CA8'), // Rich Purple
  new THREE.Color('#5B11DF'), // Bright Purple
  new THREE.Color('#7209B7'), // Vibrant Purple
  new THREE.Color('#4361EE'), // Bright Blue
  new THREE.Color('#4CC9F0'), // Electric Blue
];

const userSpeakingPalette = [
  new THREE.Color('#00FFF7'), // Cyan
  new THREE.Color('#00CFFF'), // Electric Blue
  new THREE.Color('#0080FF'), // Bright Blue
  new THREE.Color('#3B82F6'), // Blue
  new THREE.Color('#06B6D4'), // Cyan
];

const agentSpeakingPalette = [
  new THREE.Color('#0B0B3B'), // Deep Blue
  new THREE.Color('#3A0CA3'), // Deep Purple
  new THREE.Color('#480CA8'), // Rich Purple
  new THREE.Color('#5B11DF'), // Bright Purple
  new THREE.Color('#7209B7'), // Vibrant Purple
  new THREE.Color('#4361EE'), // Bright Blue
  new THREE.Color('#4CC9F0'), // Electric Blue
];

const bothSpeakingPalette = [
  new THREE.Color('#D355FF'), // Bright Purple
  new THREE.Color('#C084FC'), // Light Purple
  new THREE.Color('#A259FF'), // Purple
  new THREE.Color('#8A2BE2'), // Violet
  new THREE.Color('#9400D3'), // Dark Violet
];

export function OrbVisualization({ isListening, isSpeaking = false, isCallActive = false }: OrbVisualizationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const particleSystemRef = useRef<THREE.Points | null>(null);
  const connectionsRef = useRef<THREE.LineSegments | null>(null);
  const solidSphereRef = useRef<THREE.Mesh | null>(null);
  const frameIdRef = useRef<number>(0);
  const timeStartRef = useRef<number>(Date.now());

  useEffect(() => {
    if (!containerRef.current) return;

    // Create scene
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Create camera with 3/4 view like original
    const camera = new THREE.PerspectiveCamera(65, 
      containerRef.current.clientWidth / containerRef.current.clientHeight, 
      0.1, 1000);
    camera.position.set(180, 180, 180);
    camera.lookAt(0, 0, 0);

    // Create renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setClearColor(0x000000, 0);
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create particle system with Fibonacci sphere distribution
    const particleCount = 2000; // Reduced for performance but still impressive
    const radius = 120;
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const latitudes = new Float32Array(particleCount);
    const longitudes = new Float32Array(particleCount);

    // Fibonacci sphere algorithm for even distribution
    const goldenRatio = (1 + Math.sqrt(5)) / 2;
    const angleIncrement = Math.PI * 2 * goldenRatio;

    for (let i = 0; i < particleCount; i++) {
      const t = i / particleCount;
      const inclination = Math.acos(1 - 2 * t);
      const azimuth = angleIncrement * i;

      const x = Math.sin(inclination) * Math.cos(azimuth) * radius;
      const y = Math.sin(inclination) * Math.sin(azimuth) * radius;
      const z = Math.cos(inclination) * radius;

      positions[i * 3] = x;
      positions[i * 3 + 1] = y;
      positions[i * 3 + 2] = z;

      latitudes[i] = inclination / Math.PI;
      longitudes[i] = (azimuth / (2 * Math.PI)) % 1.0;

      colors[i * 3] = 0.2;
      colors[i * 3 + 1] = 0.4;
      colors[i * 3 + 2] = 0.8;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geometry.userData.latitudes = latitudes;
    geometry.userData.longitudes = longitudes;

    // Create particle material with glow effect
    const particleMaterial = new THREE.PointsMaterial({
      vertexColors: true,
      size: 3.0,
      blending: THREE.AdditiveBlending,
      transparent: true,
      depthWrite: false
    });

    const particleSystem = new THREE.Points(geometry, particleMaterial);
    scene.add(particleSystem);
    particleSystemRef.current = particleSystem;

    // Create mesh connections between nearby particles
    const connectionIndices: number[] = [];
    const maxConnections = 6;

    for (let i = 0; i < particleCount; i++) {
      const x1 = positions[i * 3];
      const y1 = positions[i * 3 + 1];
      const z1 = positions[i * 3 + 2];
      
      const neighbors: { index: number; distance: number }[] = [];

      for (let j = 0; j < particleCount; j++) {
        if (i === j) continue;
        
        const x2 = positions[j * 3];
        const y2 = positions[j * 3 + 1];
        const z2 = positions[j * 3 + 2];
        
        const distance = Math.sqrt((x2-x1)**2 + (y2-y1)**2 + (z2-z1)**2);
        
        if (distance < radius * 0.8) { // Only connect nearby particles
          neighbors.push({ index: j, distance });
        }
      }

      neighbors.sort((a, b) => a.distance - b.distance);
      const connectCount = Math.min(maxConnections, neighbors.length);
      
      for (let k = 0; k < connectCount; k++) {
        connectionIndices.push(i, neighbors[k].index);
      }
    }

    // Create connections geometry
    const connectionsGeometry = new THREE.BufferGeometry();
    connectionsGeometry.setIndex(connectionIndices);
    connectionsGeometry.setAttribute('position', geometry.getAttribute('position'));
    connectionsGeometry.setAttribute('color', geometry.getAttribute('color'));

    const connectionsMaterial = new THREE.LineBasicMaterial({
      vertexColors: true,
      transparent: true,
      opacity: 0.3,
      blending: THREE.AdditiveBlending,
      depthTest: false,
      depthWrite: false
    });

    const connections = new THREE.LineSegments(connectionsGeometry, connectionsMaterial);
    scene.add(connections);
    connectionsRef.current = connections;

    // Create solid sphere with advanced shader (simplified version)
    const sphereGeometry = new THREE.SphereGeometry(radius * 0.98, 64, 64);
    
    const vertexShader = `
      uniform float time;
      uniform float audioIntensity;
      varying vec2 vUv;
      varying vec3 vNormal;
      
      void main() {
        vUv = uv;
        vNormal = normalize(normalMatrix * normal);
        
        vec3 pos = position;
        float displacement = 0.0;
        
        if (audioIntensity > 0.1) {
          // Create ripple effects during conversation
          float ripple1 = sin(length(pos) * 0.1 - time * 2.0) * audioIntensity * 5.0;
          float ripple2 = sin(pos.x * 0.05 + pos.y * 0.05 - time * 1.5) * audioIntensity * 3.0;
          displacement = ripple1 + ripple2;
        } else {
          // Gentle breathing when idle
          displacement = sin(time * 0.5) * 2.0;
        }
        
        pos += normalize(pos) * displacement;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
      }
    `;

    const fragmentShader = `
      uniform float time;
      uniform float audioIntensity;
      uniform vec3 color1;
      uniform vec3 color2;
      uniform vec3 color3;
      varying vec2 vUv;
      varying vec3 vNormal;
      
      void main() {
        float pattern = sin(vUv.x * 10.0 + time) * sin(vUv.y * 10.0 + time * 0.7);
        float blend = 0.5 + 0.5 * pattern;
        
        vec3 color = mix(color1, color2, blend);
        color = mix(color, color3, audioIntensity);
        
        // Rim lighting for glow effect
        float rim = 1.0 - max(0.0, dot(vNormal, vec3(0.0, 0.0, 1.0)));
        rim = pow(rim, 2.0);
        color += vec3(0.2, 0.4, 1.0) * rim * 0.5;
        
        gl_FragColor = vec4(color, 0.8);
      }
    `;

    const shaderMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        audioIntensity: { value: 0 },
        color1: { value: new THREE.Color(palette[0]) },
        color2: { value: new THREE.Color(palette[2]) },
        color3: { value: new THREE.Color(palette[4]) }
      },
      vertexShader,
      fragmentShader,
      transparent: true,
      opacity: 0.0
    });

    const solidSphere = new THREE.Mesh(sphereGeometry, shaderMaterial);
    solidSphere.visible = false;
    scene.add(solidSphere);
    solidSphereRef.current = solidSphere;

    // Add lighting
    const ambientLight = new THREE.AmbientLight(0x404040);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(200, 200, 200);
    scene.add(directionalLight);

    // Animation loop
    const animate = () => {
      frameIdRef.current = requestAnimationFrame(animate);

      const time = (Date.now() - timeStartRef.current) * 0.001;

      // Simulate audio values
      const intensityMultiplier = isSpeaking ? 1.5 : 1.0;
      let bassIntensity: number, midIntensity: number, trebleIntensity: number;

      if (!isCallActive) {
        bassIntensity = (0.2 + 0.1 * Math.sin(time * 0.4)) * 0.5;
        midIntensity = (0.2 + 0.1 * Math.sin(time * 0.5)) * 0.5;
        trebleIntensity = (0.2 + 0.1 * Math.sin(time * 0.6)) * 0.5;
      } else {
        bassIntensity = (0.4 + 0.3 * Math.sin(time * 0.8)) * intensityMultiplier;
        midIntensity = (0.4 + 0.3 * Math.sin(time * 1.2)) * intensityMultiplier;
        trebleIntensity = (0.4 + 0.3 * Math.sin(time * 1.5)) * intensityMultiplier;
      }

      // Update particle colors based on state
      const colors = geometry.getAttribute('color') as THREE.BufferAttribute;
      const latitudes = geometry.userData.latitudes;
      const longitudes = geometry.userData.longitudes;

      let currentPalette: THREE.Color[];

      if (!isCallActive) {
        // Idle state - show wireframe with subtle colors
        for (let i = 0; i < particleCount; i++) {
          const lat = latitudes[i];
          const lon = longitudes[i];
          const colorVariation = (Math.sin(lat * 5) + Math.cos(lon * 6)) * 0.5 + 0.5;

          if (colorVariation < 0.33) {
            colors.setXYZ(i, 0.0, 0.1, 0.4);
          } else if (colorVariation < 0.66) {
            colors.setXYZ(i, 0.0, 0.5, 0.5);
          } else {
            colors.setXYZ(i, 0.3, 0.0, 0.5);
          }
        }

        if (connections && connections.material instanceof THREE.LineBasicMaterial) {
          connections.material.opacity = 0.3;
          connections.visible = true;
        }

        if (solidSphere) {
          solidSphere.visible = false;
        }
      } else {
        // Active call - show dynamic colors and solid sphere
        if (isListening && isSpeaking) {
          currentPalette = bothSpeakingPalette;
        } else if (isSpeaking) {
          currentPalette = agentSpeakingPalette;
        } else if (isListening) {
          currentPalette = userSpeakingPalette;
        } else {
          currentPalette = palette;
        }

        for (let i = 0; i < particleCount; i++) {
          const lat = latitudes[i];
          const lon = longitudes[i];

          // Create complex patterns
          const spiral = 0.5 + 0.5 * Math.cos(lon * 8 + lat * 3 + time * 0.5);
          const pulse = 0.5 + 0.5 * Math.sin(Math.abs(lat - 0.5) * 15 + time * 0.75);

          const idx1 = Math.floor(spiral * currentPalette.length) % currentPalette.length;
          const idx2 = Math.floor(pulse * currentPalette.length) % currentPalette.length;
          
          const color1 = currentPalette[idx1];
          const color2 = currentPalette[idx2];
          
          const blendFactor = 0.3 + 0.4 * Math.sin(lat * 8 - lon * 6 + time * 0.6);
          const finalColor = new THREE.Color().lerpColors(color1, color2, blendFactor);

          colors.setXYZ(i, finalColor.r, finalColor.g, finalColor.b);
        }

        if (connections && connections.material instanceof THREE.LineBasicMaterial) {
          connections.material.opacity = 0.3;
          connections.visible = true;
        }

        if (solidSphere) {
          solidSphere.visible = true;
          if (solidSphere.material instanceof THREE.ShaderMaterial) {
            solidSphere.material.uniforms.time.value = time;
            solidSphere.material.uniforms.audioIntensity.value = (bassIntensity + midIntensity + trebleIntensity) / 3;
            solidSphere.material.opacity = Math.min(0.8, solidSphere.material.opacity + 0.05);
          }
        }
      }

      colors.needsUpdate = true;

      // Enhanced rotation with audio reactivity
      const baseRotationSpeed = 0.001;
      const audioBoost = (isListening || isSpeaking) ? 2.0 : 1.0;
      
      particleSystem.rotation.y += baseRotationSpeed + bassIntensity * 0.001 * audioBoost;
      particleSystem.rotation.x = 0.15 * Math.sin(time * 0.7) + 0.05 * Math.sin(time * 0.3) * midIntensity;
      particleSystem.rotation.z = 0.05 * Math.cos(time * 0.5) + 0.03 * Math.sin(time * 0.2) * trebleIntensity;

      if (connections) {
        connections.rotation.copy(particleSystem.rotation);
      }

      renderer.render(scene, camera);
    };

    animate();

    // Cleanup
    return () => {
      if (frameIdRef.current) {
        cancelAnimationFrame(frameIdRef.current);
      }
      if (rendererRef.current && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
      }
    };
  }, [isListening, isSpeaking, isCallActive]);

  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      <div
        ref={containerRef}
        className="w-full h-full"
        style={{ background: 'transparent' }}
      />
    </div>
  );
}
