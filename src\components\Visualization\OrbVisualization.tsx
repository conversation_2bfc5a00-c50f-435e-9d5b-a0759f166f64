import { useRef, useEffect, useMemo, useCallback } from 'react';
import * as THREE from 'three';

interface OrbVisualizationProps {
  isListening: boolean;
  isSpeaking?: boolean;
}

// Updated palette to match the reference image with blue-purple glow
const palette = [
  new THREE.Color('#0B0B3B'), // Deep Blue (inner core)
  new THREE.Color('#3A0CA3'), // Deep Purple
  new THREE.Color('#480CA8'), // Rich Purple
  new THREE.Color('#5B11DF'), // Bright Purple
  new THREE.Color('#7209B7'), // Vibrant Purple
  new THREE.Color('#4361EE'), // Bright Blue (outer edge)
  new THREE.Color('#4CC9F0'), // Electric Blue (outer glow)
];

// Specific palettes for different audio states with blues and purples
const userSpeakingPalette = [
  new THREE.Color('#00FFF7'), // Cyan/Aqua
  new THREE.Color('#00CFFF'), // Electric Blue
  new THREE.Color('#0080FF'), // Bright Blue
  new THREE.Color('#3B82F6'), // Blue
  new THREE.Color('#06B6D4'), // Cyan
];

// Updated palette to match the reference image with blue-purple glow
const agentSpeakingPalette = [
  new THREE.Color('#0B0B3B'), // Deep Blue (inner core)
  new THREE.Color('#3A0CA3'), // Deep Purple
  new THREE.Color('#480CA8'), // Rich Purple
  new THREE.Color('#5B11DF'), // Bright Purple
  new THREE.Color('#7209B7'), // Vibrant Purple
  new THREE.Color('#4361EE'), // Bright Blue (outer edge)
  new THREE.Color('#4CC9F0'), // Electric Blue (outer glow)
];

const bothSpeakingPalette = [
  new THREE.Color('#D355FF'), // Bright Purple
  new THREE.Color('#C084FC'), // Light Purple
  new THREE.Color('#A259FF'), // Purple
  new THREE.Color('#8A2BE2'), // Violet
  new THREE.Color('#9400D3'), // Dark Violet
];

export function OrbVisualization({ isListening, isSpeaking = false }: OrbVisualizationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const particleSystemRef = useRef<THREE.Points | null>(null);
  const connectionsRef = useRef<THREE.LineSegments | null>(null);
  const solidSphereRef = useRef<THREE.Mesh | null>(null);
  const frameIdRef = useRef<number>(0);
  const timeStartRef = useRef<number>(Date.now());

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    console.log('Initializing 3D scene');

    // Create scene
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Create camera
    const camera = new THREE.PerspectiveCamera(
      65, // Field of view
      containerRef.current.clientWidth / containerRef.current.clientHeight, // Aspect ratio
      0.1, // Near clipping plane
      1000 // Far clipping plane
    );
    // Position camera for a 3/4 view that better shows the sphere's dimensionality
    camera.position.x = 180; // Position to the right
    camera.position.y = 180; // Position above
    camera.position.z = 180; // Position in front
    camera.lookAt(0, 0, 0); // Look at the center of the sphere
    cameraRef.current = camera;

    // Create renderer
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true // Allow transparency
    });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setClearColor(0x000000, 0); // Transparent background
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create particles geometry
    const particles = new THREE.BufferGeometry();
    const particleCount = 4000;

    // Arrays to store particle positions, initial positions, and colors
    const positions = new Float32Array(particleCount * 3);
    const initialPositions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3); // RGB colors for each particle
    const latitudes = new Float32Array(particleCount); // Store latitude (phi) for color mapping
    const longitudes = new Float32Array(particleCount); // Store longitude (theta) for color mapping

    // Create a hollow sphere with evenly distributed particles
    const radius = 120; // Reduced radius to make the sphere smaller

    // Use the Fibonacci sphere algorithm for more uniform distribution
    const goldenRatio = (1 + Math.sqrt(5)) / 2;
    const angleIncrement = Math.PI * 2 * goldenRatio;

    let idx = 0;
    for (let i = 0; i < particleCount; i++) {
      // Calculate the spherical coordinates using Fibonacci distribution
      const t = i / particleCount;
      const inclination = Math.acos(1 - 2 * t);
      const azimuth = angleIncrement * i;

      // Convert to Cartesian coordinates
      const x = Math.sin(inclination) * Math.cos(azimuth) * radius;
      const y = Math.sin(inclination) * Math.sin(azimuth) * radius;
      const z = Math.cos(inclination) * radius;

      const j = idx * 3;

      // Set position
      positions[j] = x;
      positions[j + 1] = y;
      positions[j + 2] = z;

      // Store initial position for animation
      initialPositions[j] = x;
      initialPositions[j + 1] = y;
      initialPositions[j + 2] = z;

      // Store normalized latitude and longitude for color mapping
      latitudes[idx] = inclination / Math.PI; // 0 to 1 (north to south pole)
      longitudes[idx] = (azimuth / (2 * Math.PI)) % 1.0; // 0 to 1 (around the sphere)

      // Initialize with white color (will be updated in animation loop)
      colors[j] = 1.0;     // R
      colors[j + 1] = 1.0; // G
      colors[j + 2] = 1.0; // B

      idx++;
    }

    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    particles.userData.initialPositions = initialPositions;
    particles.userData.latitudes = latitudes;
    particles.userData.longitudes = longitudes;

    // Create particle material - try to load particle.png, fallback to canvas texture
    let particleTexture: THREE.Texture;

    try {
      // Try to load the particle.png file from public folder
      particleTexture = new THREE.TextureLoader().load('/particle.png');
    } catch (e) {
      // Create a fallback texture if loading fails
      const canvas = document.createElement('canvas');
      canvas.width = 64;
      canvas.height = 64;
      const context = canvas.getContext('2d');

      if (context) {
        // Create a radial gradient for a glowing particle effect
        const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
        gradient.addColorStop(0.2, 'rgba(200, 200, 255, 0.9)');
        gradient.addColorStop(0.4, 'rgba(150, 150, 255, 0.6)');
        gradient.addColorStop(0.7, 'rgba(100, 100, 255, 0.3)');
        gradient.addColorStop(1, 'rgba(50, 50, 255, 0)');

        context.fillStyle = gradient;
        context.beginPath();
        context.arc(32, 32, 32, 0, Math.PI * 2);
        context.fill();
      }

      particleTexture = new THREE.CanvasTexture(canvas);
    }

    const particleMaterial = new THREE.PointsMaterial({
      vertexColors: true, // Use colors from vertices
      size: 3.0, // Slightly larger particles for better visibility
      map: particleTexture,
      blending: THREE.AdditiveBlending,
      transparent: true,
      depthWrite: false
    });

    // Create particle system
    const particleSystem = new THREE.Points(particles, particleMaterial);
    scene.add(particleSystem);
    particleSystemRef.current = particleSystem;

    // Create connections between particles for mesh effect with consistent appearance
    const connectionsMaterial = new THREE.LineBasicMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.3, // Consistent opacity
      blending: THREE.AdditiveBlending,
      vertexColors: true,
      linewidth: 1, // Note: linewidth > 1 not supported in WebGL, but we set it anyway
      depthTest: false, // Prevent depth testing which can cause inconsistent appearance
      depthWrite: false // Prevent writing to depth buffer
    });

    // Create connections geometry
    const connectionsGeometry = new THREE.BufferGeometry();

    // Create a more complete mesh across the entire sphere
    const connectionIndices: number[] = [];

    // Use Delaunay triangulation approach for more uniform mesh
    // We'll connect each particle to its nearest neighbors based on angular distance
    const maxConnectionsPerParticle = 6; // Each particle connects to up to 6 neighbors

    // For each particle, find its nearest neighbors
    for (let i = 0; i < particleCount; i++) {
      const j = i * 3;
      const x1 = positions[j];
      const y1 = positions[j + 1];
      const z1 = positions[j + 2];

      // Calculate normalized direction vector (for angular comparison)
      const length1 = Math.sqrt(x1 * x1 + y1 * y1 + z1 * z1);
      const nx1 = x1 / length1;
      const ny1 = y1 / length1;
      const nz1 = z1 / length1;

      // Store distances to all other particles
      interface NeighborInfo {
        index: number;
        dotProduct: number; // Higher dot product means closer angular distance
      }

      const neighbors: NeighborInfo[] = [];

      // Find angular distances to other particles
      for (let k = 0; k < particleCount; k++) {
        if (i === k) continue; // Skip self

        const m = k * 3;
        const x2 = positions[m];
        const y2 = positions[m + 1];
        const z2 = positions[m + 2];

        // Calculate normalized direction vector
        const length2 = Math.sqrt(x2 * x2 + y2 * y2 + z2 * z2);
        const nx2 = x2 / length2;
        const ny2 = y2 / length2;
        const nz2 = z2 / length2;

        // Calculate dot product (higher means closer in angle)
        const dotProduct = nx1 * nx2 + ny1 * ny2 + nz1 * nz2;

        // Only consider particles that are somewhat close (dot product > 0.7 means within ~45 degrees)
        if (dotProduct > 0.7) {
          neighbors.push({ index: k, dotProduct });
        }
      }

      // Sort by dot product (descending) to get closest neighbors first
      neighbors.sort((a, b) => b.dotProduct - a.dotProduct);

      // Connect to closest neighbors (up to maxConnectionsPerParticle)
      const connectCount = Math.min(maxConnectionsPerParticle, neighbors.length);
      for (let n = 0; n < connectCount; n++) {
        connectionIndices.push(i, neighbors[n].index);
      }
    }

    // Create index buffer for connections
    connectionsGeometry.setIndex(connectionIndices);
    connectionsGeometry.setAttribute('position', particles.getAttribute('position'));
    connectionsGeometry.setAttribute('color', particles.getAttribute('color'));

    // Create line segments
    const connections = new THREE.LineSegments(connectionsGeometry, connectionsMaterial);
    connections.renderOrder = 1; // Ensure it renders after (on top of) the solid sphere
    scene.add(connections);
    connectionsRef.current = connections;

    // Make the connections barely larger than the solid sphere
    connections.scale.set(1.005, 1.005, 1.005);

    // Create a solid sphere with smooth shading - smaller than the particle sphere to fit inside the mesh
    const sphereGeometry = new THREE.SphereGeometry(radius * 0.98, 64, 64); // 98% of the mesh size

    // Create a simple material for the solid sphere
    const sphereMaterial = new THREE.MeshBasicMaterial({
      color: new THREE.Color('#3A0CA3'),
      transparent: true,
      opacity: 0.8,
    });

    const solidSphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
    scene.add(solidSphere);
    solidSphereRef.current = solidSphere;

    // Start animation loop
    const animate = () => {
      frameIdRef.current = requestAnimationFrame(animate);

      const time = (Date.now() - timeStartRef.current) * 0.001; // Convert to seconds

      // Determine audio state: 0=inactive, 1=listening, 2=speaking, 3=both
      let audioState = 0;
      if (isListening && isSpeaking) {
        audioState = 3; // Both active
      } else if (isSpeaking) {
        audioState = 2; // Agent speaking
      } else if (isListening) {
        audioState = 1; // User speaking
      }

      // Update particle colors based on audio state
      const colors = particleSystem.geometry.getAttribute('color') as THREE.BufferAttribute;
      const latitudes = particleSystem.geometry.userData.latitudes;
      const longitudes = particleSystem.geometry.userData.longitudes;

      // Select the appropriate color palette based on audio state
      let currentPalette = palette;
      if (audioState === 1) {
        currentPalette = userSpeakingPalette;
      } else if (audioState === 2) {
        currentPalette = agentSpeakingPalette;
      } else if (audioState === 3) {
        currentPalette = bothSpeakingPalette;
      }

      // Update particle colors with smooth transitions
      for (let i = 0; i < particleCount; i++) {
        const lat = latitudes[i];
        const lon = longitudes[i];

        // Create dynamic color patterns
        const pattern1 = 0.5 + 0.5 * Math.sin(lat * 5.0 + time + lon * 3.0);
        const pattern2 = 0.5 + 0.5 * Math.sin(lon * 7.0 + time * 1.3 + lat * 2.0);

        // Select colors from palette based on patterns
        const colorIndex1 = Math.floor(pattern1 * (currentPalette.length - 1));
        const colorIndex2 = Math.floor(pattern2 * (currentPalette.length - 1));
        const nextIndex1 = Math.min(colorIndex1 + 1, currentPalette.length - 1);
        const nextIndex2 = Math.min(colorIndex2 + 1, currentPalette.length - 1);

        // Interpolate between colors
        const t1 = (pattern1 * (currentPalette.length - 1)) % 1;
        const t2 = (pattern2 * (currentPalette.length - 1)) % 1;

        const color1 = currentPalette[colorIndex1].clone().lerp(currentPalette[nextIndex1], t1);
        const color2 = currentPalette[colorIndex2].clone().lerp(currentPalette[nextIndex2], t2);

        // Final color blend
        const finalColor = color1.lerp(color2, 0.5);

        // Apply intensity boost based on audio state
        let intensityBoost = 1.0;
        if (audioState === 1) {
          intensityBoost = 1.3 + 0.4 * Math.abs(Math.sin(time * 0.9 + lat * 2.5));
        } else if (audioState === 2) {
          intensityBoost = 1.4 + 0.5 * Math.abs(Math.sin(time + lat * 2.0));
        } else if (audioState === 3) {
          intensityBoost = 1.5 + 0.6 * Math.abs(Math.sin(time * 1.2 + lat * 3.0));
        } else {
          intensityBoost = 1.1 + 0.2 * Math.abs(Math.sin(time * 0.7 + lon * 2.0));
        }

        // Apply the color with intensity boost
        const boostedColor = finalColor.multiplyScalar(intensityBoost);

        colors.setXYZ(i, boostedColor.r, boostedColor.g, boostedColor.b);
      }

      colors.needsUpdate = true;

      // Update sphere material color based on audio state
      if (solidSphere) {
        const sphereColor = currentPalette[Math.floor(currentPalette.length / 2)].clone();
        const pulseFactor = 0.8 + 0.2 * Math.sin(time * 2);
        sphereColor.multiplyScalar(pulseFactor);
        (solidSphere.material as THREE.MeshBasicMaterial).color = sphereColor;

        // Add rotation
        solidSphere.rotation.y = time * 0.1;
        solidSphere.rotation.x = time * 0.05;
      }

      // Rotate particle system and connections
      if (particleSystem) {
        particleSystem.rotation.y = time * 0.05;
        particleSystem.rotation.x = time * 0.02;
      }

      if (connections) {
        connections.rotation.y = time * 0.05;
        connections.rotation.x = time * 0.02;
      }

      // Render the scene
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };

    animate();

    // Cleanup function
    return () => {
      if (frameIdRef.current) {
        cancelAnimationFrame(frameIdRef.current);
      }
      if (rendererRef.current && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
      }
    };
  }, [isListening, isSpeaking]);

  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      <div
        ref={containerRef}
        className="w-full h-full"
        style={{ background: 'transparent' }}
      />
    </div>
  );
}
