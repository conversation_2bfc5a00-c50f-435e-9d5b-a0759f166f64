import { useRef, useEffect, useState } from 'react';

interface OrbVisualizationProps {
  isListening: boolean;
  isSpeaking?: boolean;
  isCallActive?: boolean;
}

export function OrbVisualization({ isListening, isSpeaking = false, isCallActive = false }: OrbVisualizationProps) {
  const [rotation, setRotation] = useState(0);
  const [ripplePhase, setRipplePhase] = useState(0);
  const [colorShift, setColorShift] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      const speed = isCallActive ? (isSpeaking ? 4 : 3) : 1;
      setRotation(prev => prev + speed);

      // Ripple effects - faster when active
      const rippleSpeed = isCallActive ? (isSpeaking ? 8 : 6) : 2;
      setRipplePhase(prev => prev + rippleSpeed);

      // Color shifting for fluid transitions
      const colorSpeed = isCallActive ? 3 : 1;
      setColorShift(prev => prev + colorSpeed);
    }, 50);

    return () => clearInterval(interval);
  }, [isCallActive, isSpeaking]);

  // Enhanced color system with fluid transitions
  const getFluidColors = () => {
    const shift = colorShift * 0.02; // Slow color shifting

    if (!isCallActive) {
      // Idle state - subtle shifting blues/grays
      return {
        primary: `hsl(${210 + Math.sin(shift) * 20}, 25%, ${25 + Math.cos(shift * 0.7) * 5}%)`,
        secondary: `hsl(${215 + Math.cos(shift * 0.8) * 15}, 20%, ${35 + Math.sin(shift * 0.9) * 5}%)`,
        accent: `hsl(${220 + Math.sin(shift * 1.1) * 10}, 15%, ${45 + Math.cos(shift * 0.6) * 5}%)`
      };
    } else if (isListening && isSpeaking) {
      // Both speaking - vibrant shifting purple/pink/cyan
      return {
        primary: `hsl(${320 + Math.sin(shift) * 40}, ${70 + Math.cos(shift * 0.8) * 20}%, ${60 + Math.sin(shift * 0.9) * 10}%)`,
        secondary: `hsl(${260 + Math.cos(shift * 0.7) * 30}, ${65 + Math.sin(shift * 1.1) * 25}%, ${65 + Math.cos(shift * 0.6) * 10}%)`,
        accent: `hsl(${190 + Math.sin(shift * 1.2) * 25}, ${75 + Math.cos(shift * 0.9) * 15}%, ${55 + Math.sin(shift * 0.8) * 10}%)`
      };
    } else if (isSpeaking) {
      // Agent speaking - fluid green/blue/teal
      return {
        primary: `hsl(${160 + Math.sin(shift) * 30}, ${70 + Math.cos(shift * 0.9) * 20}%, ${50 + Math.sin(shift * 0.7) * 15}%)`,
        secondary: `hsl(${190 + Math.cos(shift * 0.8) * 25}, ${75 + Math.sin(shift * 1.0) * 15}%, ${55 + Math.cos(shift * 0.6) * 10}%)`,
        accent: `hsl(${220 + Math.sin(shift * 1.1) * 20}, ${80 + Math.cos(shift * 0.7) * 10}%, ${60 + Math.sin(shift * 0.9) * 10}%)`
      };
    } else if (isListening) {
      // User speaking - flowing cyan/blue/purple
      return {
        primary: `hsl(${190 + Math.sin(shift) * 25}, ${75 + Math.cos(shift * 0.8) * 15}%, ${55 + Math.sin(shift * 0.9) * 10}%)`,
        secondary: `hsl(${220 + Math.cos(shift * 0.7) * 20}, ${70 + Math.sin(shift * 1.0) * 20}%, ${60 + Math.cos(shift * 0.6) * 10}%)`,
        accent: `hsl(${260 + Math.sin(shift * 1.2) * 30}, ${65 + Math.cos(shift * 0.9) * 25}%, ${65 + Math.sin(shift * 0.8) * 10}%)`
      };
    } else {
      // Default active state - shifting indigo/purple/cyan
      return {
        primary: `hsl(${240 + Math.sin(shift) * 20}, ${70 + Math.cos(shift * 0.8) * 15}%, ${55 + Math.sin(shift * 0.9) * 10}%)`,
        secondary: `hsl(${270 + Math.cos(shift * 0.7) * 25}, ${65 + Math.sin(shift * 1.1) * 20}%, ${60 + Math.cos(shift * 0.6) * 10}%)`,
        accent: `hsl(${190 + Math.sin(shift * 1.0) * 15}, ${75 + Math.cos(shift * 0.9) * 10}%, ${55 + Math.sin(shift * 0.8) * 10}%)`
      };
    }
  };

  const colors = getFluidColors();

  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      {/* Main circular gradient orb with ripple effects */}
      <div
        className="relative w-80 h-80 rounded-full overflow-hidden transition-all duration-500 ease-out"
        style={{
          background: `conic-gradient(from ${rotation + Math.sin(ripplePhase * 0.05) * 10}deg,
            ${colors.primary} ${0 + Math.cos(ripplePhase * 0.03) * 5}deg,
            ${colors.secondary} ${60 + Math.sin(ripplePhase * 0.04) * 8}deg,
            ${colors.accent} ${120 + Math.cos(ripplePhase * 0.035) * 6}deg,
            ${colors.primary} ${180 + Math.sin(ripplePhase * 0.045) * 7}deg,
            ${colors.secondary} ${240 + Math.cos(ripplePhase * 0.038) * 9}deg,
            ${colors.accent} ${300 + Math.sin(ripplePhase * 0.042) * 5}deg,
            ${colors.primary} ${360 + Math.cos(ripplePhase * 0.036) * 4}deg
          )`,
          transform: `scale(${1 + (isCallActive ? 0.05 : -0.05) + Math.sin(ripplePhase * 0.08) * (isCallActive ? 0.03 : 0.01)})`,
          filter: `blur(${isCallActive ? '0px' : '1px'}) hue-rotate(${Math.sin(colorShift * 0.01) * 10}deg)`,
          opacity: isCallActive ? 1 : 0.7
        }}
      >
        {/* Solid center fill with ripple effects */}
        <div
          className="absolute inset-0 rounded-full transition-all duration-300"
          style={{
            background: `radial-gradient(circle at ${50 + Math.sin(ripplePhase * 0.06) * 5}% ${50 + Math.cos(ripplePhase * 0.07) * 5}%,
              ${colors.primary}${Math.floor(40 + Math.sin(ripplePhase * 0.08) * 15)} ${Math.max(0, 10 + Math.cos(ripplePhase * 0.09) * 10)}%,
              ${colors.secondary}${Math.floor(30 + Math.cos(ripplePhase * 0.07) * 12)} ${Math.max(20, 35 + Math.sin(ripplePhase * 0.06) * 15)}%,
              ${colors.accent}${Math.floor(20 + Math.sin(ripplePhase * 0.05) * 10)} ${Math.max(50, 65 + Math.cos(ripplePhase * 0.08) * 10)}%,
              transparent 100%
            )`,
            opacity: isCallActive ? (0.8 + Math.sin(ripplePhase * 0.1) * 0.1) : (0.4 + Math.cos(ripplePhase * 0.05) * 0.05)
          }}
        />

        {/* Additional ripple layer */}
        <div
          className="absolute inset-0 rounded-full transition-all duration-400"
          style={{
            background: `radial-gradient(circle at ${50 + Math.cos(ripplePhase * 0.04) * 8}% ${50 + Math.sin(ripplePhase * 0.05) * 8}%,
              transparent 0%,
              ${colors.accent}${Math.floor(15 + Math.sin(ripplePhase * 0.12) * 8)} ${Math.max(30, 40 + Math.cos(ripplePhase * 0.11) * 15)}%,
              ${colors.primary}${Math.floor(10 + Math.cos(ripplePhase * 0.09) * 6)} ${Math.max(60, 70 + Math.sin(ripplePhase * 0.1) * 10)}%,
              transparent 100%
            )`,
            opacity: isCallActive ? (0.4 + Math.cos(ripplePhase * 0.08) * 0.1) : (0.2 + Math.sin(ripplePhase * 0.04) * 0.05)
          }}
        />

        {/* Fluid animated overlay with ripples */}
        <div
          className="absolute inset-0 rounded-full transition-opacity duration-300"
          style={{
            background: `conic-gradient(from ${-rotation * 0.5 + Math.sin(ripplePhase * 0.03) * 15}deg,
              transparent ${0 + Math.cos(ripplePhase * 0.04) * 10}deg,
              ${colors.accent}${Math.floor(25 + Math.sin(ripplePhase * 0.06) * 10)} ${90 + Math.cos(ripplePhase * 0.05) * 15}deg,
              transparent ${180 + Math.sin(ripplePhase * 0.035) * 12}deg,
              ${colors.secondary}${Math.floor(25 + Math.cos(ripplePhase * 0.055) * 8)} ${270 + Math.sin(ripplePhase * 0.045) * 18}deg,
              transparent ${360 + Math.cos(ripplePhase * 0.04) * 8}deg
            )`,
            opacity: isCallActive ? (0.4 + Math.sin(ripplePhase * 0.07) * 0.1) : (0.15 + Math.cos(ripplePhase * 0.03) * 0.05),
            transform: `scale(${1 + Math.sin(ripplePhase * 0.09) * (isCallActive ? 0.02 : 0.005)})`
          }}
        />

        {/* Secondary fluid counter-rotating layer */}
        <div
          className="absolute inset-2 rounded-full transition-opacity duration-400"
          style={{
            background: `conic-gradient(from ${rotation * 0.3 + Math.cos(ripplePhase * 0.04) * 20}deg,
              ${colors.primary}${Math.floor(20 + Math.sin(ripplePhase * 0.08) * 12)} ${0 + Math.cos(ripplePhase * 0.06) * 8}deg,
              transparent ${60 + Math.sin(ripplePhase * 0.05) * 15}deg,
              ${colors.accent}${Math.floor(15 + Math.cos(ripplePhase * 0.07) * 8)} ${120 + Math.sin(ripplePhase * 0.04) * 12}deg,
              transparent ${180 + Math.cos(ripplePhase * 0.055) * 10}deg,
              ${colors.secondary}${Math.floor(20 + Math.sin(ripplePhase * 0.065) * 10)} ${240 + Math.cos(ripplePhase * 0.045) * 14}deg,
              transparent ${300 + Math.sin(ripplePhase * 0.035) * 8}deg,
              ${colors.primary}${Math.floor(15 + Math.cos(ripplePhase * 0.06) * 6)} ${360 + Math.sin(ripplePhase * 0.05) * 5}deg
            )`,
            opacity: isCallActive ? (0.3 + Math.cos(ripplePhase * 0.06) * 0.08) : (0.1 + Math.sin(ripplePhase * 0.025) * 0.03),
            transform: `scale(${1 + Math.cos(ripplePhase * 0.07) * (isCallActive ? 0.015 : 0.003)}) rotate(${Math.sin(ripplePhase * 0.02) * 2}deg)`
          }}
        />

        {/* Tertiary ripple wave layer */}
        <div
          className="absolute inset-4 rounded-full transition-opacity duration-600"
          style={{
            background: `radial-gradient(circle at ${50 + Math.sin(ripplePhase * 0.03) * 10}% ${50 + Math.cos(ripplePhase * 0.04) * 10}%,
              ${colors.accent}${Math.floor(8 + Math.sin(ripplePhase * 0.1) * 5)} ${Math.max(20, 30 + Math.cos(ripplePhase * 0.08) * 20)}%,
              transparent ${Math.max(40, 50 + Math.sin(ripplePhase * 0.06) * 15)}%,
              ${colors.primary}${Math.floor(5 + Math.cos(ripplePhase * 0.09) * 3)} ${Math.max(70, 80 + Math.sin(ripplePhase * 0.07) * 10)}%,
              transparent 100%
            )`,
            opacity: isCallActive ? (0.2 + Math.sin(ripplePhase * 0.05) * 0.05) : (0.08 + Math.cos(ripplePhase * 0.02) * 0.02)
          }}
        />
      </div>

      {/* Enhanced outer glow with ripple effects */}
      <div
        className="absolute inset-0 rounded-full transition-all duration-500"
        style={{
          background: `radial-gradient(circle at ${50 + Math.sin(ripplePhase * 0.02) * 3}% ${50 + Math.cos(ripplePhase * 0.025) * 3}%,
            transparent ${Math.max(55, 60 + Math.sin(ripplePhase * 0.04) * 8)}%,
            ${colors.primary}${Math.floor(10 + Math.cos(ripplePhase * 0.06) * 5)} ${Math.max(65, 70 + Math.sin(ripplePhase * 0.05) * 10)}%,
            ${colors.secondary}${Math.floor(5 + Math.sin(ripplePhase * 0.07) * 3)} ${Math.max(75, 80 + Math.cos(ripplePhase * 0.045) * 8)}%,
            transparent ${Math.max(85, 90 + Math.sin(ripplePhase * 0.03) * 5)}%
          )`,
          transform: `scale(${1.1 + (isCallActive ? 0.1 : 0) + Math.sin(ripplePhase * 0.06) * (isCallActive ? 0.05 : 0.02)})`,
          opacity: isCallActive ? (0.8 + Math.cos(ripplePhase * 0.08) * 0.1) : (0.3 + Math.sin(ripplePhase * 0.04) * 0.05)
        }}
      />

      {/* Additional outer ripple ring */}
      <div
        className="absolute inset-0 rounded-full transition-all duration-800"
        style={{
          background: `radial-gradient(circle at center,
            transparent ${Math.max(70, 75 + Math.cos(ripplePhase * 0.035) * 10)}%,
            ${colors.accent}${Math.floor(3 + Math.sin(ripplePhase * 0.09) * 2)} ${Math.max(80, 85 + Math.sin(ripplePhase * 0.04) * 8)}%,
            transparent ${Math.max(90, 95 + Math.cos(ripplePhase * 0.03) * 3)}%
          )`,
          transform: `scale(${1.3 + Math.cos(ripplePhase * 0.05) * (isCallActive ? 0.08 : 0.03)})`,
          opacity: isCallActive ? (0.4 + Math.sin(ripplePhase * 0.06) * 0.1) : (0.15 + Math.cos(ripplePhase * 0.025) * 0.03)
        }}
      />
    </div>
  );
}
