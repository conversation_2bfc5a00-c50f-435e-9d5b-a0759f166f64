import { useRef, useEffect, useState } from 'react';

interface OrbVisualizationProps {
  isListening: boolean;
  isSpeaking?: boolean;
  isCallActive?: boolean;
}

export function OrbVisualization({ isListening, isSpeaking = false, isCallActive = false }: OrbVisualizationProps) {
  const [rotation, setRotation] = useState(0);
  const [audioIntensity, setAudioIntensity] = useState(0);
  const [bassLevel, setBassLevel] = useState(0);
  const [midLevel, setMidLevel] = useState(0);
  const [trebleLevel, setTrebleLevel] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate realistic audio analysis
      if (isCallActive && (isListening || isSpeaking)) {
        // Simulate speech patterns - more realistic audio
        const time = Date.now() * 0.001;
        const speechPattern = Math.abs(Math.sin(time * 2.5)) * Math.abs(Math.cos(time * 1.8)) * Math.abs(Math.sin(time * 3.2));
        const intensity = 0.2 + speechPattern * 0.8;

        // Simulate frequency bands
        setBassLevel(0.1 + Math.abs(Math.sin(time * 1.2)) * intensity * 0.6);
        setMidLevel(0.2 + Math.abs(Math.cos(time * 2.1)) * intensity * 0.8);
        setTrebleLevel(0.1 + Math.abs(Math.sin(time * 3.5)) * intensity * 0.7);

        setAudioIntensity(intensity);
      } else {
        // Idle state - minimal activity
        setBassLevel(0.05);
        setMidLevel(0.08);
        setTrebleLevel(0.03);
        setAudioIntensity(0.1);
      }

      // Simple, steady rotation based on audio
      const rotationSpeed = isCallActive ? (0.5 + audioIntensity * 1.5) : 0.3;
      setRotation(prev => prev + rotationSpeed);
    }, 50);

    return () => clearInterval(interval);
  }, [isCallActive, isSpeaking, isListening]);

  // Simplified audio-reactive color system
  const getAudioReactiveColors = () => {
    if (!isCallActive) {
      // Idle state - static deep blue/purple
      return {
        primary: `hsl(230, 40%, 35%)`,
        secondary: `hsl(220, 45%, 40%)`,
        accent: `hsl(200, 50%, 45%)`
      };
    } else if (isSpeaking) {
      // Agent speaking - teal/green spectrum that reacts to audio
      const hueShift = bassLevel * 20; // Bass affects hue
      const saturation = 60 + midLevel * 25; // Mids affect saturation
      const lightness = 45 + trebleLevel * 15; // Treble affects brightness

      return {
        primary: `hsl(${170 + hueShift}, ${saturation}%, ${lightness}%)`,
        secondary: `hsl(${190 + hueShift * 0.8}, ${saturation * 0.9}%, ${lightness * 1.1}%)`,
        accent: `hsl(${210 + hueShift * 0.6}, ${saturation * 0.8}%, ${lightness * 0.9}%)`
      };
    } else if (isListening) {
      // User speaking - blue/purple spectrum that reacts to audio
      const hueShift = bassLevel * 15;
      const saturation = 65 + midLevel * 20;
      const lightness = 50 + trebleLevel * 12;

      return {
        primary: `hsl(${220 + hueShift}, ${saturation}%, ${lightness}%)`,
        secondary: `hsl(${240 + hueShift * 0.7}, ${saturation * 0.9}%, ${lightness * 1.05}%)`,
        accent: `hsl(${200 + hueShift * 0.5}, ${saturation * 0.85}%, ${lightness * 0.95}%)`
      };
    } else {
      // Default active - balanced that reacts to audio
      const hueShift = bassLevel * 12;
      const saturation = 55 + midLevel * 18;
      const lightness = 48 + trebleLevel * 10;

      return {
        primary: `hsl(${225 + hueShift}, ${saturation}%, ${lightness}%)`,
        secondary: `hsl(${205 + hueShift * 0.8}, ${saturation * 0.9}%, ${lightness * 1.08}%)`,
        accent: `hsl(${185 + hueShift * 0.6}, ${saturation * 0.85}%, ${lightness * 0.92}%)`
      };
    }
  };

  const colors = getAudioReactiveColors();

  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      {/* Main circular gradient orb - simplified and audio-reactive */}
      <div
        className="relative w-80 h-80 rounded-full overflow-hidden transition-all duration-200 ease-out"
        style={{
          background: `conic-gradient(from ${rotation}deg,
            ${colors.primary} ${0 + bassLevel * 15}deg,
            ${colors.secondary} ${60 + midLevel * 20}deg,
            ${colors.accent} ${120 + trebleLevel * 18}deg,
            ${colors.primary} ${180 + bassLevel * 12}deg,
            ${colors.secondary} ${240 + midLevel * 16}deg,
            ${colors.accent} ${300 + trebleLevel * 14}deg,
            ${colors.primary} ${360 + bassLevel * 10}deg
          )`,
          transform: `scale(${0.95 + (isCallActive ? 0.1 + audioIntensity * 0.15 : 0)})`,
          filter: `blur(${isCallActive ? '0px' : '1px'})`,
          opacity: isCallActive ? (0.9 + audioIntensity * 0.1) : 0.7
        }}
      >
        {/* Audio-reactive center fill */}
        <div
          className="absolute inset-0 rounded-full transition-all duration-150"
          style={{
            background: `radial-gradient(circle at center,
              ${colors.primary}${Math.floor(30 + audioIntensity * 25)} ${Math.max(0, 15 + bassLevel * 20)}%,
              ${colors.secondary}${Math.floor(20 + audioIntensity * 20)} ${Math.max(30, 40 + midLevel * 25)}%,
              ${colors.accent}${Math.floor(15 + audioIntensity * 15)} ${Math.max(60, 70 + trebleLevel * 20)}%,
              transparent 100%
            )`,
            opacity: isCallActive ? (0.6 + audioIntensity * 0.3) : 0.3
          }}
        />

        {/* Simple audio-reactive overlay */}
        <div
          className="absolute inset-0 rounded-full transition-opacity duration-100"
          style={{
            background: `conic-gradient(from ${-rotation * 0.5}deg,
              transparent 0deg,
              ${colors.accent}${Math.floor(20 + midLevel * 15)} ${90 + bassLevel * 30}deg,
              transparent 180deg,
              ${colors.secondary}${Math.floor(20 + trebleLevel * 12)} ${270 + midLevel * 25}deg,
              transparent 360deg
            )`,
            opacity: isCallActive ? (0.3 + audioIntensity * 0.2) : 0.1
          }}
        />
      </div>

      {/* Audio-reactive outer glow */}
      <div
        className="absolute inset-0 rounded-full transition-all duration-200"
        style={{
          background: `radial-gradient(circle at center,
            transparent 60%,
            ${colors.primary}${Math.floor(8 + audioIntensity * 12)} ${70 + bassLevel * 15}%,
            ${colors.secondary}${Math.floor(4 + audioIntensity * 8)} ${80 + midLevel * 10}%,
            transparent ${90 + trebleLevel * 8}%
          )`,
          transform: `scale(${1.1 + (isCallActive ? 0.1 + audioIntensity * 0.2 : 0)})`,
          opacity: isCallActive ? (0.6 + audioIntensity * 0.3) : 0.2
        }}
      />
    </div>
  );
}
