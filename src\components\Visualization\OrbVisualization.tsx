import { useRef, useEffect, useState } from 'react';

interface OrbVisualizationProps {
  isListening: boolean;
  isSpeaking?: boolean;
  isCallActive?: boolean;
}

export function OrbVisualization({ isListening, isSpeaking = false, isCallActive = false }: OrbVisualizationProps) {
  const [rotation, setRotation] = useState(0);
  const [counterRotation, setCounterRotation] = useState(0);
  const [ripplePhase, setRipplePhase] = useState(0);
  const [colorShift, setColorShift] = useState(0);
  const [audioIntensity, setAudioIntensity] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      // Dynamic rotation - changes direction and speed
      const baseSpeed = isCallActive ? (isSpeaking ? 2 : 1.5) : 0.8;
      const dynamicSpeed = baseSpeed + Math.sin(Date.now() * 0.001) * 0.5;
      setRotation(prev => prev + dynamicSpeed);

      // Counter rotation for layered effect
      const counterSpeed = isCallActive ? (isSpeaking ? -1.5 : -1) : -0.5;
      const dynamicCounterSpeed = counterSpeed + Math.cos(Date.now() * 0.0008) * 0.3;
      setCounterRotation(prev => prev + dynamicCounterSpeed);

      // Sound-reactive ripples
      const baseRippleSpeed = isCallActive ? (isSpeaking ? 6 : 4) : 1.5;
      // Simulate audio reactivity with varying intensity
      const simulatedAudio = isCallActive ?
        (0.3 + 0.7 * Math.abs(Math.sin(Date.now() * 0.003)) * Math.abs(Math.cos(Date.now() * 0.005))) :
        (0.1 + 0.2 * Math.abs(Math.sin(Date.now() * 0.001)));

      setAudioIntensity(simulatedAudio);
      const audioReactiveSpeed = baseRippleSpeed * (1 + simulatedAudio * 2);
      setRipplePhase(prev => prev + audioReactiveSpeed);

      // Smoother color shifting
      const colorSpeed = isCallActive ? 1.5 : 0.8;
      setColorShift(prev => prev + colorSpeed);
    }, 50);

    return () => clearInterval(interval);
  }, [isCallActive, isSpeaking]);

  // Refined color system - purple/blue/teal/green palette with smoother transitions
  const getFluidColors = () => {
    const shift = colorShift * 0.015; // Slower, smoother color shifting
    const audioBoost = audioIntensity * 0.3; // Audio-reactive color intensity

    if (!isCallActive) {
      // Idle state - deep purple/blue tones
      return {
        primary: `hsl(${240 + Math.sin(shift) * 15}, ${35 + Math.cos(shift * 0.7) * 10}%, ${30 + Math.sin(shift * 0.8) * 8}%)`,
        secondary: `hsl(${220 + Math.cos(shift * 0.6) * 12}, ${40 + Math.sin(shift * 0.9) * 8}%, ${35 + Math.cos(shift * 0.5) * 6}%)`,
        accent: `hsl(${200 + Math.sin(shift * 0.8) * 10}, ${45 + Math.cos(shift * 0.7) * 6}%, ${40 + Math.sin(shift * 0.6) * 5}%)`
      };
    } else if (isListening && isSpeaking) {
      // Both speaking - vibrant purple to teal spectrum
      return {
        primary: `hsl(${280 + Math.sin(shift) * 20 + audioBoost * 15}, ${65 + Math.cos(shift * 0.8) * 15 + audioBoost * 10}%, ${55 + Math.sin(shift * 0.9) * 12 + audioBoost * 8}%)`,
        secondary: `hsl(${200 + Math.cos(shift * 0.7) * 25 + audioBoost * 12}, ${70 + Math.sin(shift * 1.0) * 12 + audioBoost * 8}%, ${60 + Math.cos(shift * 0.6) * 10 + audioBoost * 6}%)`,
        accent: `hsl(${160 + Math.sin(shift * 1.1) * 18 + audioBoost * 10}, ${75 + Math.cos(shift * 0.9) * 10 + audioBoost * 6}%, ${50 + Math.sin(shift * 0.8) * 8 + audioBoost * 5}%)`
      };
    } else if (isSpeaking) {
      // Agent speaking - teal to green spectrum
      return {
        primary: `hsl(${170 + Math.sin(shift) * 18 + audioBoost * 12}, ${65 + Math.cos(shift * 0.9) * 12 + audioBoost * 8}%, ${50 + Math.sin(shift * 0.7) * 10 + audioBoost * 6}%)`,
        secondary: `hsl(${190 + Math.cos(shift * 0.8) * 15 + audioBoost * 10}, ${70 + Math.sin(shift * 1.0) * 10 + audioBoost * 6}%, ${55 + Math.cos(shift * 0.6) * 8 + audioBoost * 5}%)`,
        accent: `hsl(${220 + Math.sin(shift * 1.1) * 12 + audioBoost * 8}, ${75 + Math.cos(shift * 0.7) * 8 + audioBoost * 5}%, ${60 + Math.sin(shift * 0.9) * 6 + audioBoost * 4}%)`
      };
    } else if (isListening) {
      // User speaking - blue to purple spectrum
      return {
        primary: `hsl(${210 + Math.sin(shift) * 15 + audioBoost * 10}, ${70 + Math.cos(shift * 0.8) * 10 + audioBoost * 6}%, ${55 + Math.sin(shift * 0.9) * 8 + audioBoost * 5}%)`,
        secondary: `hsl(${250 + Math.cos(shift * 0.7) * 18 + audioBoost * 8}, ${65 + Math.sin(shift * 1.0) * 12 + audioBoost * 7}%, ${60 + Math.cos(shift * 0.6) * 6 + audioBoost * 4}%)`,
        accent: `hsl(${180 + Math.sin(shift * 1.2) * 12 + audioBoost * 6}, ${75 + Math.cos(shift * 0.9) * 8 + audioBoost * 4}%, ${50 + Math.sin(shift * 0.8) * 5 + audioBoost * 3}%)`
      };
    } else {
      // Default active state - balanced purple/blue/teal
      return {
        primary: `hsl(${230 + Math.sin(shift) * 12 + audioBoost * 8}, ${60 + Math.cos(shift * 0.8) * 8 + audioBoost * 5}%, ${50 + Math.sin(shift * 0.9) * 6 + audioBoost * 4}%)`,
        secondary: `hsl(${200 + Math.cos(shift * 0.7) * 15 + audioBoost * 6}, ${65 + Math.sin(shift * 1.0) * 10 + audioBoost * 4}%, ${55 + Math.cos(shift * 0.6) * 5 + audioBoost * 3}%)`,
        accent: `hsl(${180 + Math.sin(shift * 1.1) * 10 + audioBoost * 5}, ${70 + Math.cos(shift * 0.9) * 6 + audioBoost * 3}%, ${45 + Math.sin(shift * 0.8) * 4 + audioBoost * 2}%)`
      };
    }
  };

  const colors = getFluidColors();

  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      {/* Main circular gradient orb with dynamic motion and sound reactivity */}
      <div
        className="relative w-80 h-80 rounded-full overflow-hidden transition-all duration-300 ease-out"
        style={{
          background: `conic-gradient(from ${rotation + Math.sin(ripplePhase * 0.04) * (15 + audioIntensity * 20)}deg,
            ${colors.primary} ${0 + Math.cos(ripplePhase * 0.03) * (8 + audioIntensity * 12)}deg,
            ${colors.secondary} ${60 + Math.sin(ripplePhase * 0.035) * (10 + audioIntensity * 15)}deg,
            ${colors.accent} ${120 + Math.cos(ripplePhase * 0.04) * (6 + audioIntensity * 10)}deg,
            ${colors.primary} ${180 + Math.sin(ripplePhase * 0.038) * (9 + audioIntensity * 14)}deg,
            ${colors.secondary} ${240 + Math.cos(ripplePhase * 0.042) * (12 + audioIntensity * 18)}deg,
            ${colors.accent} ${300 + Math.sin(ripplePhase * 0.036) * (7 + audioIntensity * 11)}deg,
            ${colors.primary} ${360 + Math.cos(ripplePhase * 0.045) * (5 + audioIntensity * 8)}deg
          )`,
          transform: `scale(${1 + (isCallActive ? 0.05 : -0.05) + Math.sin(ripplePhase * 0.06) * (isCallActive ? 0.04 + audioIntensity * 0.03 : 0.01)})
                     rotate(${Math.sin(counterRotation * 0.02) * (2 + audioIntensity * 3)}deg)`,
          filter: `blur(${isCallActive ? '0px' : '1px'}) hue-rotate(${Math.sin(colorShift * 0.008) * (5 + audioIntensity * 8)}deg)`,
          opacity: isCallActive ? (0.95 + audioIntensity * 0.05) : 0.7
        }}
      >
        {/* Solid center fill with ripple effects */}
        <div
          className="absolute inset-0 rounded-full transition-all duration-300"
          style={{
            background: `radial-gradient(circle at ${50 + Math.sin(ripplePhase * 0.06) * 5}% ${50 + Math.cos(ripplePhase * 0.07) * 5}%,
              ${colors.primary}${Math.floor(40 + Math.sin(ripplePhase * 0.08) * 15)} ${Math.max(0, 10 + Math.cos(ripplePhase * 0.09) * 10)}%,
              ${colors.secondary}${Math.floor(30 + Math.cos(ripplePhase * 0.07) * 12)} ${Math.max(20, 35 + Math.sin(ripplePhase * 0.06) * 15)}%,
              ${colors.accent}${Math.floor(20 + Math.sin(ripplePhase * 0.05) * 10)} ${Math.max(50, 65 + Math.cos(ripplePhase * 0.08) * 10)}%,
              transparent 100%
            )`,
            opacity: isCallActive ? (0.8 + Math.sin(ripplePhase * 0.1) * 0.1) : (0.4 + Math.cos(ripplePhase * 0.05) * 0.05)
          }}
        />

        {/* Additional ripple layer */}
        <div
          className="absolute inset-0 rounded-full transition-all duration-400"
          style={{
            background: `radial-gradient(circle at ${50 + Math.cos(ripplePhase * 0.04) * 8}% ${50 + Math.sin(ripplePhase * 0.05) * 8}%,
              transparent 0%,
              ${colors.accent}${Math.floor(15 + Math.sin(ripplePhase * 0.12) * 8)} ${Math.max(30, 40 + Math.cos(ripplePhase * 0.11) * 15)}%,
              ${colors.primary}${Math.floor(10 + Math.cos(ripplePhase * 0.09) * 6)} ${Math.max(60, 70 + Math.sin(ripplePhase * 0.1) * 10)}%,
              transparent 100%
            )`,
            opacity: isCallActive ? (0.4 + Math.cos(ripplePhase * 0.08) * 0.1) : (0.2 + Math.sin(ripplePhase * 0.04) * 0.05)
          }}
        />

        {/* Dynamic counter-rotating overlay */}
        <div
          className="absolute inset-0 rounded-full transition-opacity duration-200"
          style={{
            background: `conic-gradient(from ${counterRotation + Math.sin(ripplePhase * 0.025) * (20 + audioIntensity * 25)}deg,
              transparent ${0 + Math.cos(ripplePhase * 0.035) * (12 + audioIntensity * 15)}deg,
              ${colors.accent}${Math.floor(25 + Math.sin(ripplePhase * 0.05) * (12 + audioIntensity * 8))} ${90 + Math.cos(ripplePhase * 0.04) * (18 + audioIntensity * 12)}deg,
              transparent ${180 + Math.sin(ripplePhase * 0.03) * (15 + audioIntensity * 10)}deg,
              ${colors.secondary}${Math.floor(25 + Math.cos(ripplePhase * 0.045) * (10 + audioIntensity * 6))} ${270 + Math.sin(ripplePhase * 0.038) * (22 + audioIntensity * 14)}deg,
              transparent ${360 + Math.cos(ripplePhase * 0.032) * (10 + audioIntensity * 8)}deg
            )`,
            opacity: isCallActive ? (0.4 + Math.sin(ripplePhase * 0.06) * 0.15 + audioIntensity * 0.1) : (0.15 + Math.cos(ripplePhase * 0.025) * 0.05),
            transform: `scale(${1 + Math.sin(ripplePhase * 0.08) * (isCallActive ? 0.03 + audioIntensity * 0.02 : 0.008)})
                       rotate(${Math.cos(counterRotation * 0.015) * (3 + audioIntensity * 4)}deg)`
          }}
        />

        {/* Secondary dynamic layer with different rotation */}
        <div
          className="absolute inset-2 rounded-full transition-opacity duration-250"
          style={{
            background: `conic-gradient(from ${-counterRotation * 0.7 + Math.cos(ripplePhase * 0.028) * (25 + audioIntensity * 20)}deg,
              ${colors.primary}${Math.floor(20 + Math.sin(ripplePhase * 0.07) * (15 + audioIntensity * 10))} ${0 + Math.cos(ripplePhase * 0.05) * (10 + audioIntensity * 8)}deg,
              transparent ${60 + Math.sin(ripplePhase * 0.042) * (18 + audioIntensity * 12)}deg,
              ${colors.accent}${Math.floor(15 + Math.cos(ripplePhase * 0.06) * (10 + audioIntensity * 6))} ${120 + Math.sin(ripplePhase * 0.035) * (15 + audioIntensity * 10)}deg,
              transparent ${180 + Math.cos(ripplePhase * 0.048) * (12 + audioIntensity * 8)}deg,
              ${colors.secondary}${Math.floor(20 + Math.sin(ripplePhase * 0.055) * (12 + audioIntensity * 8))} ${240 + Math.cos(ripplePhase * 0.04) * (16 + audioIntensity * 12)}deg,
              transparent ${300 + Math.sin(ripplePhase * 0.03) * (10 + audioIntensity * 6)}deg,
              ${colors.primary}${Math.floor(15 + Math.cos(ripplePhase * 0.052) * (8 + audioIntensity * 5))} ${360 + Math.sin(ripplePhase * 0.045) * (8 + audioIntensity * 6)}deg
            )`,
            opacity: isCallActive ? (0.3 + Math.cos(ripplePhase * 0.05) * 0.12 + audioIntensity * 0.08) : (0.1 + Math.sin(ripplePhase * 0.02) * 0.03),
            transform: `scale(${1 + Math.cos(ripplePhase * 0.06) * (isCallActive ? 0.02 + audioIntensity * 0.015 : 0.005)})
                       rotate(${Math.sin(rotation * 0.008) * (4 + audioIntensity * 5)}deg)`
          }}
        />

        {/* Tertiary ripple wave layer */}
        <div
          className="absolute inset-4 rounded-full transition-opacity duration-600"
          style={{
            background: `radial-gradient(circle at ${50 + Math.sin(ripplePhase * 0.03) * 10}% ${50 + Math.cos(ripplePhase * 0.04) * 10}%,
              ${colors.accent}${Math.floor(8 + Math.sin(ripplePhase * 0.1) * 5)} ${Math.max(20, 30 + Math.cos(ripplePhase * 0.08) * 20)}%,
              transparent ${Math.max(40, 50 + Math.sin(ripplePhase * 0.06) * 15)}%,
              ${colors.primary}${Math.floor(5 + Math.cos(ripplePhase * 0.09) * 3)} ${Math.max(70, 80 + Math.sin(ripplePhase * 0.07) * 10)}%,
              transparent 100%
            )`,
            opacity: isCallActive ? (0.2 + Math.sin(ripplePhase * 0.05) * 0.05) : (0.08 + Math.cos(ripplePhase * 0.02) * 0.02)
          }}
        />
      </div>

      {/* Enhanced outer glow with ripple effects */}
      <div
        className="absolute inset-0 rounded-full transition-all duration-500"
        style={{
          background: `radial-gradient(circle at ${50 + Math.sin(ripplePhase * 0.02) * 3}% ${50 + Math.cos(ripplePhase * 0.025) * 3}%,
            transparent ${Math.max(55, 60 + Math.sin(ripplePhase * 0.04) * 8)}%,
            ${colors.primary}${Math.floor(10 + Math.cos(ripplePhase * 0.06) * 5)} ${Math.max(65, 70 + Math.sin(ripplePhase * 0.05) * 10)}%,
            ${colors.secondary}${Math.floor(5 + Math.sin(ripplePhase * 0.07) * 3)} ${Math.max(75, 80 + Math.cos(ripplePhase * 0.045) * 8)}%,
            transparent ${Math.max(85, 90 + Math.sin(ripplePhase * 0.03) * 5)}%
          )`,
          transform: `scale(${1.1 + (isCallActive ? 0.1 : 0) + Math.sin(ripplePhase * 0.06) * (isCallActive ? 0.05 : 0.02)})`,
          opacity: isCallActive ? (0.8 + Math.cos(ripplePhase * 0.08) * 0.1) : (0.3 + Math.sin(ripplePhase * 0.04) * 0.05)
        }}
      />

      {/* Additional outer ripple ring */}
      <div
        className="absolute inset-0 rounded-full transition-all duration-800"
        style={{
          background: `radial-gradient(circle at center,
            transparent ${Math.max(70, 75 + Math.cos(ripplePhase * 0.035) * 10)}%,
            ${colors.accent}${Math.floor(3 + Math.sin(ripplePhase * 0.09) * 2)} ${Math.max(80, 85 + Math.sin(ripplePhase * 0.04) * 8)}%,
            transparent ${Math.max(90, 95 + Math.cos(ripplePhase * 0.03) * 3)}%
          )`,
          transform: `scale(${1.3 + Math.cos(ripplePhase * 0.05) * (isCallActive ? 0.08 : 0.03)})`,
          opacity: isCallActive ? (0.4 + Math.sin(ripplePhase * 0.06) * 0.1) : (0.15 + Math.cos(ripplePhase * 0.025) * 0.03)
        }}
      />
    </div>
  );
}
