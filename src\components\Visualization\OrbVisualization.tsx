import { useRef, useEffect, useState } from 'react';

interface OrbVisualizationProps {
  isListening: boolean;
  isSpeaking?: boolean;
  isCallActive?: boolean;
}

export function OrbVisualization({ isListening, isSpeaking = false, isCallActive = false }: OrbVisualizationProps) {
  const [rotation, setRotation] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      const speed = isCallActive ? (isSpeaking ? 4 : 3) : 1;
      setRotation(prev => prev + speed);
    }, 50);

    return () => clearInterval(interval);
  }, [isCallActive, isSpeaking]);

  // Determine the gradient colors based on state
  const getGradientColors = () => {
    if (!isCallActive) {
      // Idle state - subtle blue/gray
      return {
        primary: '#1e293b',
        secondary: '#334155',
        accent: '#475569'
      };
    } else if (isListening && isSpeaking) {
      // Both speaking - vibrant purple/pink
      return {
        primary: '#ec4899',
        secondary: '#8b5cf6',
        accent: '#06b6d4'
      };
    } else if (isSpeaking) {
      // Agent speaking - green/blue like the image
      return {
        primary: '#10b981',
        secondary: '#06b6d4',
        accent: '#3b82f6'
      };
    } else if (isListening) {
      // User speaking - cyan/blue
      return {
        primary: '#06b6d4',
        secondary: '#3b82f6',
        accent: '#8b5cf6'
      };
    } else {
      // Default active state
      return {
        primary: '#6366f1',
        secondary: '#8b5cf6',
        accent: '#06b6d4'
      };
    }
  };

  const colors = getGradientColors();

  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      {/* Main circular gradient orb */}
      <div
        className="relative w-80 h-80 rounded-full overflow-hidden transition-all duration-700 ease-in-out"
        style={{
          background: `conic-gradient(from ${rotation}deg,
            ${colors.primary} 0deg,
            ${colors.secondary} 60deg,
            ${colors.accent} 120deg,
            ${colors.primary} 180deg,
            ${colors.secondary} 240deg,
            ${colors.accent} 300deg,
            ${colors.primary} 360deg
          )`,
          transform: `scale(${isCallActive ? 1.05 : 0.95})`,
          filter: `blur(${isCallActive ? '0px' : '1px'})`,
          opacity: isCallActive ? 1 : 0.7
        }}
      >
        {/* Solid center fill - no donut hole */}
        <div
          className="absolute inset-0 rounded-full transition-all duration-700"
          style={{
            background: `radial-gradient(circle at center,
              ${colors.primary}40 0%,
              ${colors.secondary}30 30%,
              ${colors.accent}20 60%,
              transparent 100%
            )`,
            opacity: isCallActive ? 0.8 : 0.4
          }}
        />

        {/* Animated overlay for extra visual interest */}
        <div
          className="absolute inset-0 rounded-full transition-opacity duration-500"
          style={{
            background: `conic-gradient(from ${-rotation * 0.5}deg,
              transparent 0deg,
              ${colors.accent}25 90deg,
              transparent 180deg,
              ${colors.secondary}25 270deg,
              transparent 360deg
            )`,
            opacity: isCallActive ? 0.4 : 0.15
          }}
        />

        {/* Secondary counter-rotating layer */}
        <div
          className="absolute inset-2 rounded-full transition-opacity duration-500"
          style={{
            background: `conic-gradient(from ${rotation * 0.3}deg,
              ${colors.primary}20 0deg,
              transparent 60deg,
              ${colors.accent}15 120deg,
              transparent 180deg,
              ${colors.secondary}20 240deg,
              transparent 300deg,
              ${colors.primary}15 360deg
            )`,
            opacity: isCallActive ? 0.3 : 0.1
          }}
        />
      </div>

      {/* Outer glow effect */}
      <div
        className="absolute inset-0 rounded-full transition-all duration-700"
        style={{
          background: `radial-gradient(circle at center,
            transparent 60%,
            ${colors.primary}10 70%,
            ${colors.secondary}05 80%,
            transparent 90%
          )`,
          transform: `scale(${isCallActive ? 1.2 : 1.1})`,
          opacity: isCallActive ? 0.8 : 0.3
        }}
      />
    </div>
  );
}
