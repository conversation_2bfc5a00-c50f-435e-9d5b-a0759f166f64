import { useRef, useEffect, useMemo, useCallback } from 'react';
import * as THREE from 'three';

interface OrbVisualizationProps {
  isListening: boolean;
  isSpeaking?: boolean;
  isCallActive?: boolean;
}

// Clean wireframe palette for idle state
const idlePalette = [
  new THREE.Color('#F8F8F8'), // Soft white
  new THREE.Color('#E8E8E8'), // Light gray
  new THREE.Color('#D8D8D8'), // Medium gray
  new THREE.Color('#60E0D0'), // Soft teal
  new THREE.Color('#50C8B8'), // Muted teal
];

// Dynamic palette for active call state - more muted colors
const activePalette = [
  new THREE.Color('#2A2A5A'), // Muted deep blue
  new THREE.Color('#4A2C8A'), // Muted purple
  new THREE.Color('#5A3C9A'), // Soft purple
  new THREE.Color('#6A4CAA'), // Light purple
  new THREE.Color('#5A6CEE'), // Soft blue
  new THREE.Color('#6CC9F0'), // Light blue
];

// Specific palettes for different audio states - more muted and smooth
const userSpeakingPalette = [
  new THREE.Color('#40D0E0'), // Soft cyan
  new THREE.Color('#50B8E0'), // Muted blue
  new THREE.Color('#60A0E0'), // Light blue
  new THREE.Color('#5090D0'), // Soft blue
  new THREE.Color('#4080C0'), // Gentle blue
];

// Agent speaking palette - softer purples
const agentSpeakingPalette = [
  new THREE.Color('#6A4CAA'), // Soft purple
  new THREE.Color('#7A5CBA'), // Light purple
  new THREE.Color('#8A6CCA'), // Gentle purple
  new THREE.Color('#9A7CDA'), // Pale purple
  new THREE.Color('#AA8CEA'), // Very light purple
];

const bothSpeakingPalette = [
  new THREE.Color('#8A7CDA'), // Soft purple
  new THREE.Color('#9A8CE0'), // Light purple
  new THREE.Color('#AA9CE6'), // Pale purple
  new THREE.Color('#BAACEC'), // Very light purple
  new THREE.Color('#CABCF2'), // Ultra light purple
];

export function OrbVisualization({ isListening, isSpeaking = false, isCallActive = false }: OrbVisualizationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const particleSystemRef = useRef<THREE.Points | null>(null);
  const connectionsRef = useRef<THREE.LineSegments | null>(null);
  const solidSphereRef = useRef<THREE.Mesh | null>(null);
  const frameIdRef = useRef<number>(0);
  const timeStartRef = useRef<number>(Date.now());

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    console.log('Initializing 3D scene');

    // Create scene
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Create camera
    const camera = new THREE.PerspectiveCamera(
      65, // Field of view
      containerRef.current.clientWidth / containerRef.current.clientHeight, // Aspect ratio
      0.1, // Near clipping plane
      1000 // Far clipping plane
    );
    // Position camera for a 3/4 view that better shows the sphere's dimensionality
    camera.position.x = 180; // Position to the right
    camera.position.y = 180; // Position above
    camera.position.z = 180; // Position in front
    camera.lookAt(0, 0, 0); // Look at the center of the sphere
    cameraRef.current = camera;

    // Create renderer
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true // Allow transparency
    });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setClearColor(0x000000, 0); // Transparent background
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create particles geometry
    const particles = new THREE.BufferGeometry();
    const particleCount = 4000;

    // Arrays to store particle positions, initial positions, and colors
    const positions = new Float32Array(particleCount * 3);
    const initialPositions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3); // RGB colors for each particle
    const latitudes = new Float32Array(particleCount); // Store latitude (phi) for color mapping
    const longitudes = new Float32Array(particleCount); // Store longitude (theta) for color mapping

    // Create a hollow sphere with evenly distributed particles
    const radius = 120; // Reduced radius to make the sphere smaller

    // Use the Fibonacci sphere algorithm for more uniform distribution
    const goldenRatio = (1 + Math.sqrt(5)) / 2;
    const angleIncrement = Math.PI * 2 * goldenRatio;

    let idx = 0;
    for (let i = 0; i < particleCount; i++) {
      // Calculate the spherical coordinates using Fibonacci distribution
      const t = i / particleCount;
      const inclination = Math.acos(1 - 2 * t);
      const azimuth = angleIncrement * i;

      // Convert to Cartesian coordinates
      const x = Math.sin(inclination) * Math.cos(azimuth) * radius;
      const y = Math.sin(inclination) * Math.sin(azimuth) * radius;
      const z = Math.cos(inclination) * radius;

      const j = idx * 3;

      // Set position
      positions[j] = x;
      positions[j + 1] = y;
      positions[j + 2] = z;

      // Store initial position for animation
      initialPositions[j] = x;
      initialPositions[j + 1] = y;
      initialPositions[j + 2] = z;

      // Store normalized latitude and longitude for color mapping
      latitudes[idx] = inclination / Math.PI; // 0 to 1 (north to south pole)
      longitudes[idx] = (azimuth / (2 * Math.PI)) % 1.0; // 0 to 1 (around the sphere)

      // Initialize with white color (will be updated in animation loop)
      colors[j] = 1.0;     // R
      colors[j + 1] = 1.0; // G
      colors[j + 2] = 1.0; // B

      idx++;
    }

    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    particles.userData.initialPositions = initialPositions;
    particles.userData.latitudes = latitudes;
    particles.userData.longitudes = longitudes;

    // Create particle material - try to load particle.png, fallback to canvas texture
    let particleTexture: THREE.Texture;

    try {
      // Try to load the particle.png file from public folder
      particleTexture = new THREE.TextureLoader().load('/particle.png');
    } catch (e) {
      // Create a fallback texture if loading fails
      const canvas = document.createElement('canvas');
      canvas.width = 64;
      canvas.height = 64;
      const context = canvas.getContext('2d');

      if (context) {
        // Create a radial gradient for a glowing particle effect
        const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
        gradient.addColorStop(0.2, 'rgba(200, 200, 255, 0.9)');
        gradient.addColorStop(0.4, 'rgba(150, 150, 255, 0.6)');
        gradient.addColorStop(0.7, 'rgba(100, 100, 255, 0.3)');
        gradient.addColorStop(1, 'rgba(50, 50, 255, 0)');

        context.fillStyle = gradient;
        context.beginPath();
        context.arc(32, 32, 32, 0, Math.PI * 2);
        context.fill();
      }

      particleTexture = new THREE.CanvasTexture(canvas);
    }

    const particleMaterial = new THREE.PointsMaterial({
      vertexColors: true, // Use colors from vertices
      size: 3.0, // Slightly larger particles for better visibility
      map: particleTexture,
      blending: THREE.AdditiveBlending,
      transparent: true,
      depthWrite: false
    });

    // Create particle system
    const particleSystem = new THREE.Points(particles, particleMaterial);
    scene.add(particleSystem);
    particleSystemRef.current = particleSystem;

    // Create connections between particles for mesh effect with consistent appearance
    const connectionsMaterial = new THREE.LineBasicMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.3, // Consistent opacity
      blending: THREE.AdditiveBlending,
      vertexColors: true,
      linewidth: 1, // Note: linewidth > 1 not supported in WebGL, but we set it anyway
      depthTest: false, // Prevent depth testing which can cause inconsistent appearance
      depthWrite: false // Prevent writing to depth buffer
    });

    // Create connections geometry
    const connectionsGeometry = new THREE.BufferGeometry();

    // Create a more complete mesh across the entire sphere
    const connectionIndices: number[] = [];

    // Use Delaunay triangulation approach for more uniform mesh
    // We'll connect each particle to its nearest neighbors based on angular distance
    const maxConnectionsPerParticle = 6; // Each particle connects to up to 6 neighbors

    // For each particle, find its nearest neighbors
    for (let i = 0; i < particleCount; i++) {
      const j = i * 3;
      const x1 = positions[j];
      const y1 = positions[j + 1];
      const z1 = positions[j + 2];

      // Calculate normalized direction vector (for angular comparison)
      const length1 = Math.sqrt(x1 * x1 + y1 * y1 + z1 * z1);
      const nx1 = x1 / length1;
      const ny1 = y1 / length1;
      const nz1 = z1 / length1;

      // Store distances to all other particles
      interface NeighborInfo {
        index: number;
        dotProduct: number; // Higher dot product means closer angular distance
      }

      const neighbors: NeighborInfo[] = [];

      // Find angular distances to other particles
      for (let k = 0; k < particleCount; k++) {
        if (i === k) continue; // Skip self

        const m = k * 3;
        const x2 = positions[m];
        const y2 = positions[m + 1];
        const z2 = positions[m + 2];

        // Calculate normalized direction vector
        const length2 = Math.sqrt(x2 * x2 + y2 * y2 + z2 * z2);
        const nx2 = x2 / length2;
        const ny2 = y2 / length2;
        const nz2 = z2 / length2;

        // Calculate dot product (higher means closer in angle)
        const dotProduct = nx1 * nx2 + ny1 * ny2 + nz1 * nz2;

        // Only consider particles that are somewhat close (dot product > 0.7 means within ~45 degrees)
        if (dotProduct > 0.7) {
          neighbors.push({ index: k, dotProduct });
        }
      }

      // Sort by dot product (descending) to get closest neighbors first
      neighbors.sort((a, b) => b.dotProduct - a.dotProduct);

      // Connect to closest neighbors (up to maxConnectionsPerParticle)
      const connectCount = Math.min(maxConnectionsPerParticle, neighbors.length);
      for (let n = 0; n < connectCount; n++) {
        connectionIndices.push(i, neighbors[n].index);
      }
    }

    // Create index buffer for connections
    connectionsGeometry.setIndex(connectionIndices);
    connectionsGeometry.setAttribute('position', particles.getAttribute('position'));
    connectionsGeometry.setAttribute('color', particles.getAttribute('color'));

    // Create line segments
    const connections = new THREE.LineSegments(connectionsGeometry, connectionsMaterial);
    connections.renderOrder = 1; // Ensure it renders after (on top of) the solid sphere
    scene.add(connections);
    connectionsRef.current = connections;

    // Make the connections barely larger than the solid sphere
    connections.scale.set(1.005, 1.005, 1.005);

    // Create a solid sphere with smooth shading - smaller than the particle sphere to fit inside the mesh
    const sphereGeometry = new THREE.SphereGeometry(radius * 0.98, 64, 64); // 98% of the mesh size

    // Create a simple material for the solid sphere
    const sphereMaterial = new THREE.MeshBasicMaterial({
      color: new THREE.Color('#3A0CA3'),
      transparent: true,
      opacity: 0.8,
    });

    const solidSphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
    scene.add(solidSphere);
    solidSphereRef.current = solidSphere;

    // Start animation loop
    const animate = () => {
      frameIdRef.current = requestAnimationFrame(animate);

      const time = (Date.now() - timeStartRef.current) * 0.001; // Convert to seconds

      // Update particle colors based on call state
      const colors = particleSystem.geometry.getAttribute('color') as THREE.BufferAttribute;
      const latitudes = particleSystem.geometry.userData.latitudes;
      const longitudes = particleSystem.geometry.userData.longitudes;

      let currentPalette;
      let animationIntensity;
      let rotationSpeed;

      if (!isCallActive) {
        // Idle state: clean wireframe look
        currentPalette = idlePalette;
        animationIntensity = 0.05; // Very subtle animation
        rotationSpeed = 0.005; // Very slow rotation
      } else {
        // Active call state: smooth colors based on audio
        if (isListening && isSpeaking) {
          currentPalette = bothSpeakingPalette;
          animationIntensity = 0.4; // Reduced intensity for smoother animation
        } else if (isSpeaking) {
          currentPalette = agentSpeakingPalette;
          animationIntensity = 0.3; // Reduced agent speaking intensity
        } else if (isListening) {
          currentPalette = userSpeakingPalette;
          animationIntensity = 0.25; // Reduced user speaking intensity
        } else {
          currentPalette = activePalette;
          animationIntensity = 0.15; // Gentle activity during call
        }
        rotationSpeed = 0.02; // Slower rotation during call
      }

      // Update particle colors with smooth transitions
      for (let i = 0; i < particleCount; i++) {
        const lat = latitudes[i];
        const lon = longitudes[i];

        let finalColor;

        if (!isCallActive) {
          // Idle state: static wireframe with minimal variation
          const baseColorIndex = Math.floor((lat + lon) * 1.5) % currentPalette.length;
          finalColor = currentPalette[baseColorIndex].clone();

          // Very subtle pulsing for idle state
          const pulse = 0.9 + 0.1 * Math.sin(time * 0.3);
          finalColor.multiplyScalar(pulse);
        } else {
          // Active call state: smooth color patterns
          const pattern1 = 0.5 + 0.5 * Math.sin(lat * 2.0 + time * 0.5 + lon * 1.5);
          const pattern2 = 0.5 + 0.5 * Math.sin(lon * 2.5 + time * 0.7 + lat * 1.2);

          // Use smoother color selection with more gradual transitions
          const colorFloat1 = pattern1 * (currentPalette.length - 1);
          const colorFloat2 = pattern2 * (currentPalette.length - 1);

          const colorIndex1 = Math.floor(colorFloat1);
          const colorIndex2 = Math.floor(colorFloat2);
          const nextIndex1 = Math.min(colorIndex1 + 1, currentPalette.length - 1);
          const nextIndex2 = Math.min(colorIndex2 + 1, currentPalette.length - 1);

          // Smooth interpolation between colors
          const t1 = colorFloat1 - colorIndex1;
          const t2 = colorFloat2 - colorIndex2;

          const color1 = currentPalette[colorIndex1].clone().lerp(currentPalette[nextIndex1], t1);
          const color2 = currentPalette[colorIndex2].clone().lerp(currentPalette[nextIndex2], t2);

          // Gentle color blend with more weight on the base color
          finalColor = color1.lerp(color2, 0.3);

          // Subtle intensity variation
          const intensityBoost = 1.0 + animationIntensity * 0.2 * Math.sin(time * 0.5 + lat + lon);
          finalColor.multiplyScalar(intensityBoost);
        }

        colors.setXYZ(i, finalColor.r, finalColor.g, finalColor.b);
      }

      colors.needsUpdate = true;

      // Update sphere material color based on call state
      if (solidSphere) {
        const sphereColor = currentPalette[Math.floor(currentPalette.length / 2)].clone();

        if (!isCallActive) {
          // Idle state: very subtle pulsing
          const pulseFactor = 0.7 + 0.05 * Math.sin(time * 0.3);
          sphereColor.multiplyScalar(pulseFactor);
          (solidSphere.material as THREE.MeshBasicMaterial).opacity = 0.2; // Very transparent when idle
        } else {
          // Active call state: gentle pulsing
          const pulseFactor = 0.8 + 0.1 * Math.sin(time * 0.8);
          sphereColor.multiplyScalar(pulseFactor);
          (solidSphere.material as THREE.MeshBasicMaterial).opacity = 0.4; // Semi-transparent during call
        }

        (solidSphere.material as THREE.MeshBasicMaterial).color = sphereColor;

        // Add gentle rotation based on call state
        solidSphere.rotation.y = time * rotationSpeed * 1.5;
        solidSphere.rotation.x = time * rotationSpeed * 0.8;
      }

      // Rotate particle system and connections based on call state
      if (particleSystem) {
        particleSystem.rotation.y = time * rotationSpeed;
        particleSystem.rotation.x = time * rotationSpeed * 0.4;
      }

      if (connections) {
        connections.rotation.y = time * rotationSpeed;
        connections.rotation.x = time * rotationSpeed * 0.4;
      }

      // Render the scene
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };

    animate();

    // Cleanup function
    return () => {
      if (frameIdRef.current) {
        cancelAnimationFrame(frameIdRef.current);
      }
      if (rendererRef.current && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
      }
    };
  }, [isListening, isSpeaking, isCallActive]);

  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      <div
        ref={containerRef}
        className="w-full h-full"
        style={{ background: 'transparent' }}
      />
    </div>
  );
}
