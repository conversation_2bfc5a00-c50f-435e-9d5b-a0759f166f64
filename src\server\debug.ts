import express from 'express';
import cors from 'cors';
import { config } from '../api/config.js';

const app = express();

app.use(cors());
app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Debug server running' });
});

// Try loading routes one by one to identify the problematic one
console.log('Loading health routes...');
try {
  const healthRouter = (await import('./routes/health.js')).default;
  app.use('/api/health', healthRouter);
  console.log('✓ Health routes loaded successfully');
} catch (error) {
  console.error('✗ Error loading health routes:', error);
}

console.log('Loading calls routes...');
try {
  const callsRouter = (await import('./routes/calls.js')).default;
  app.use('/api/calls', callsRouter);
  console.log('✓ Calls routes loaded successfully');
} catch (error) {
  console.error('✗ Error loading calls routes:', error);
}

console.log('Loading assistants routes...');
try {
  const assistantsRouter = (await import('./routes/assistants.js')).default;
  app.use('/api/assistants', assistantsRouter);
  console.log('✓ Assistants routes loaded successfully');
} catch (error) {
  console.error('✗ Error loading assistants routes:', error);
}

console.log('Loading phone numbers routes...');
try {
  const phoneNumbersRouter = (await import('./routes/phoneNumbers.js')).default;
  app.use('/api/phone-numbers', phoneNumbersRouter);
  console.log('✓ Phone numbers routes loaded successfully');
} catch (error) {
  console.error('✗ Error loading phone numbers routes:', error);
}

console.log('Loading templates routes...');
try {
  const templatesRouter = (await import('./routes/templates.js')).default;
  app.use('/api/templates', templatesRouter);
  console.log('✓ Templates routes loaded successfully');
} catch (error) {
  console.error('✗ Error loading templates routes:', error);
}

console.log('Loading analytics routes...');
try {
  const analyticsRouter = (await import('./routes/analytics.js')).default;
  app.use('/api/analytics', analyticsRouter);
  console.log('✓ Analytics routes loaded successfully');
} catch (error) {
  console.error('✗ Error loading analytics routes:', error);
}

console.log('Loading webhooks routes...');
try {
  const webhooksRouter = (await import('./routes/webhooks.js')).default;
  app.use('/api/webhooks', webhooksRouter);
  console.log('✓ Webhooks routes loaded successfully');
} catch (error) {
  console.error('✗ Error loading webhooks routes:', error);
}

const port = config.server.port;
app.listen(port, () => {
  console.log(`🚀 Debug server running on port ${port}`);
});
