{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server:dev": "tsx watch src/server/index.ts", "server:build": "tsc --project tsconfig.server.json", "server:start": "node dist/server/index.js", "server:prod": "npm run server:build && npm run server:start"}, "dependencies": {"@types/three": "^0.176.0", "@vapi-ai/server-sdk": "^0.8.1", "@vapi-ai/web": "^2.3.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "lucide-react": "^0.344.0", "morgan": "^1.10.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "three": "^0.176.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/morgan": "^1.9.9", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tsx": "^4.19.4", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}