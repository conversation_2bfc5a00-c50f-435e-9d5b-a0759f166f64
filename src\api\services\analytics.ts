import { BaseApiService } from '../base.js';
import {
  ApiResponse,
  ErrorResponse,
  AnalyticsRequest,
  AnalyticsQuery,
  Vapi
} from '../types.js';

/**
 * Analytics and Insights Service
 * Handles analytics queries, call statistics, and performance metrics
 */
export class AnalyticsService extends BaseApiService {

  /**
   * Execute analytics queries
   */
  async executeQuery(request: AnalyticsRequest): Promise<ApiResponse<Vapi.AnalyticsQueryResult[]> | ErrorResponse> {
    this.logOperation('executeQuery', request);

    return this.handleApiCall(async () => {
      // Validate required fields
      this.validateRequired(request, ['queries']);

      if (!Array.isArray(request.queries) || request.queries.length === 0) {
        throw new Error('At least one query is required');
      }

      // Execute the analytics query using Vapi client
      return await this.client.analytics.get({
        queries: request.queries,
      });
    }, 'Analytics query executed successfully');
  }

  /**
   * Get call statistics for a date range
   */
  async getCallStats(
    dateRange?: { start: string; end: string },
    assistantId?: string,
    phoneNumberId?: string
  ): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getCallStats', { dateRange, assistantId, phoneNumberId });

    return this.handleApiCall(async () => {
      // Build query parameters
      const queryParams: any = {};
      if (dateRange?.start) queryParams.createdAtGte = dateRange.start;
      if (dateRange?.end) queryParams.createdAtLte = dateRange.end;
      if (assistantId) queryParams.assistantId = assistantId;
      if (phoneNumberId) queryParams.phoneNumberId = phoneNumberId;

      // Get calls for the specified criteria
      const calls = await this.client.calls.list(queryParams);

      // Calculate statistics
      const totalCalls = calls.length;
      const completedCalls = calls.filter(call => call.status === 'completed').length;
      const failedCalls = calls.filter(call => call.status === 'failed').length;
      const inProgressCalls = calls.filter(call => call.status === 'in-progress').length;

      const totalDuration = calls.reduce((sum, call) => {
        if (call.endedAt && call.startedAt) {
          return sum + (new Date(call.endedAt).getTime() - new Date(call.startedAt).getTime());
        }
        return sum;
      }, 0);

      const totalCost = calls.reduce((sum, call) => sum + (call.cost || 0), 0);

      const averageDuration = totalCalls > 0 ? totalDuration / totalCalls : 0;
      const successRate = totalCalls > 0 ? (completedCalls / totalCalls) * 100 : 0;

      // Group calls by day for trend analysis
      const callsByDay = calls.reduce((acc, call) => {
        const date = new Date(call.createdAt).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        summary: {
          totalCalls,
          completedCalls,
          failedCalls,
          inProgressCalls,
          successRate: Math.round(successRate * 100) / 100,
          totalDuration: Math.round(totalDuration / 1000), // Convert to seconds
          averageDuration: Math.round(averageDuration / 1000), // Convert to seconds
          totalCost: Math.round(totalCost * 100) / 100,
          averageCost: totalCalls > 0 ? Math.round((totalCost / totalCalls) * 100) / 100 : 0,
        },
        trends: {
          callsByDay,
        },
        dateRange: dateRange || { start: 'all-time', end: 'now' },
        filters: {
          assistantId: assistantId || 'all',
          phoneNumberId: phoneNumberId || 'all',
        },
        lastUpdated: new Date().toISOString(),
      };
    }, 'Call statistics retrieved successfully');
  }

  /**
   * Get lead attribution and campaign data
   */
  async getLeadStats(
    dateRange?: { start: string; end: string },
    campaignId?: string
  ): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getLeadStats', { dateRange, campaignId });

    return this.handleApiCall(async () => {
      // Build query parameters
      const queryParams: any = {};
      if (dateRange?.start) queryParams.createdAtGte = dateRange.start;
      if (dateRange?.end) queryParams.createdAtLte = dateRange.end;

      // Get calls for the specified criteria
      const calls = await this.client.calls.list(queryParams);

      // Filter by campaign if specified (assuming campaign info is in metadata)
      const filteredCalls = campaignId
        ? calls.filter(call => call.metadata?.campaignId === campaignId)
        : calls;

      // Calculate lead metrics
      const totalLeads = filteredCalls.length;
      const qualifiedLeads = filteredCalls.filter(call =>
        call.metadata?.leadQuality === 'qualified' || call.metadata?.isQualified === true
      ).length;

      const convertedLeads = filteredCalls.filter(call =>
        call.metadata?.converted === true || call.metadata?.status === 'converted'
      ).length;

      // Group by lead source
      const leadsBySource = filteredCalls.reduce((acc, call) => {
        const source = call.metadata?.source || 'unknown';
        acc[source] = (acc[source] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Group by campaign
      const leadsByCampaign = filteredCalls.reduce((acc, call) => {
        const campaign = call.metadata?.campaignId || 'unknown';
        acc[campaign] = (acc[campaign] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const qualificationRate = totalLeads > 0 ? (qualifiedLeads / totalLeads) * 100 : 0;
      const conversionRate = qualifiedLeads > 0 ? (convertedLeads / qualifiedLeads) * 100 : 0;

      return {
        summary: {
          totalLeads,
          qualifiedLeads,
          convertedLeads,
          qualificationRate: Math.round(qualificationRate * 100) / 100,
          conversionRate: Math.round(conversionRate * 100) / 100,
        },
        breakdown: {
          leadsBySource,
          leadsByCampaign,
        },
        dateRange: dateRange || { start: 'all-time', end: 'now' },
        filters: {
          campaignId: campaignId || 'all',
        },
        lastUpdated: new Date().toISOString(),
      };
    }, 'Lead statistics retrieved successfully');
  }

  /**
   * Get agent-specific success metrics
   */
  async getAgentMetrics(
    agentId: string,
    dateRange?: { start: string; end: string }
  ): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getAgentMetrics', { agentId, dateRange });

    return this.handleApiCall(async () => {
      const validAgentId = this.validateId(agentId, 'agent ID');

      // Build query parameters
      const queryParams: any = { assistantId: validAgentId };
      if (dateRange?.start) queryParams.createdAtGte = dateRange.start;
      if (dateRange?.end) queryParams.createdAtLte = dateRange.end;

      // Get calls for this agent
      const calls = await this.client.calls.list(queryParams);

      // Get agent details
      const agent = await this.client.assistants.get(validAgentId);

      // Calculate metrics
      const totalCalls = calls.length;
      const successfulCalls = calls.filter(call => call.status === 'completed').length;
      const failedCalls = calls.filter(call => call.status === 'failed').length;

      const totalDuration = calls.reduce((sum, call) => {
        if (call.endedAt && call.startedAt) {
          return sum + (new Date(call.endedAt).getTime() - new Date(call.startedAt).getTime());
        }
        return sum;
      }, 0);

      const totalCost = calls.reduce((sum, call) => sum + (call.cost || 0), 0);
      const averageDuration = totalCalls > 0 ? totalDuration / totalCalls : 0;
      const successRate = totalCalls > 0 ? (successfulCalls / totalCalls) * 100 : 0;

      // Calculate customer satisfaction (if available in metadata)
      const ratingsData = calls
        .map(call => call.metadata?.customerRating)
        .filter(rating => rating !== undefined && rating !== null);

      const averageRating = ratingsData.length > 0
        ? ratingsData.reduce((sum, rating) => sum + rating, 0) / ratingsData.length
        : null;

      return {
        agentId: validAgentId,
        agentName: agent.name,
        metrics: {
          totalCalls,
          successfulCalls,
          failedCalls,
          successRate: Math.round(successRate * 100) / 100,
          averageDuration: Math.round(averageDuration / 1000), // Convert to seconds
          totalCost: Math.round(totalCost * 100) / 100,
          averageCost: totalCalls > 0 ? Math.round((totalCost / totalCalls) * 100) / 100 : 0,
          averageRating: averageRating ? Math.round(averageRating * 100) / 100 : null,
          totalRatings: ratingsData.length,
        },
        dateRange: dateRange || { start: 'all-time', end: 'now' },
        lastUpdated: new Date().toISOString(),
      };
    }, 'Agent metrics retrieved successfully');
  }

  /**
   * Get real-time dashboard data
   */
  async getDashboardData(): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getDashboardData');

    return this.handleApiCall(async () => {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
      const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();

      // Get calls for different time periods
      const [todayCalls, weekCalls, monthCalls, allCalls] = await Promise.all([
        this.client.calls.list({ createdAtGte: today }),
        this.client.calls.list({ createdAtGte: thisWeek }),
        this.client.calls.list({ createdAtGte: thisMonth }),
        this.client.calls.list(),
      ]);

      // Get assistants and phone numbers
      const [assistants, phoneNumbers] = await Promise.all([
        this.client.assistants.list(),
        this.client.phoneNumbers.list(),
      ]);

      // Calculate key metrics
      const activeCalls = allCalls.filter(call => call.status === 'in-progress').length;
      const todayCallsCount = todayCalls.length;
      const weekCallsCount = weekCalls.length;
      const monthCallsCount = monthCalls.length;

      const todaySuccessRate = todayCalls.length > 0
        ? (todayCalls.filter(call => call.status === 'completed').length / todayCalls.length) * 100
        : 0;

      return {
        overview: {
          activeCalls,
          todayCallsCount,
          weekCallsCount,
          monthCallsCount,
          todaySuccessRate: Math.round(todaySuccessRate * 100) / 100,
          totalAssistants: assistants.length,
          totalPhoneNumbers: phoneNumbers.length,
        },
        recentActivity: {
          recentCalls: allCalls.slice(0, 10), // Last 10 calls
        },
        timestamp: new Date().toISOString(),
      };
    }, 'Dashboard data retrieved successfully');
  }
}
