import React, { createContext, useContext, useState } from 'react';

interface ModalContextType {
  isUrlModalOpen: boolean;
  isFilesModalOpen: boolean;
  isTextModalOpen: boolean;
  openUrlModal: () => void;
  openFilesModal: () => void;
  openTextModal: () => void;
  closeModals: () => void;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

export const ModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isUrlModalOpen, setIsUrlModalOpen] = useState(false);
  const [isFilesModalOpen, setIsFilesModalOpen] = useState(false);
  const [isTextModalOpen, setIsTextModalOpen] = useState(false);

  const openUrlModal = () => {
    setIsUrlModalOpen(true);
    setIsFilesModalOpen(false);
    setIsTextModalOpen(false);
  };

  const openFilesModal = () => {
    setIsUrlModalOpen(false);
    setIsFilesModalOpen(true);
    setIsTextModalOpen(false);
  };

  const openTextModal = () => {
    setIsUrlModalOpen(false);
    setIsFilesModalOpen(false);
    setIsTextModalOpen(true);
  };

  const closeModals = () => {
    setIsUrlModalOpen(false);
    setIsFilesModalOpen(false);
    setIsTextModalOpen(false);
  };

  return (
    <ModalContext.Provider
      value={{
        isUrlModalOpen,
        isFilesModalOpen,
        isTextModalOpen,
        openUrlModal,
        openFilesModal,
        openTextModal,
        closeModals,
      }}
    >
      {children}
    </ModalContext.Provider>
  );
};

export const useModal = () => {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};