import { useState, useEffect, useCallback } from 'react';
import { apiClient, Call, CreateCallRequest } from '../services/api';

export const useCalls = () => {
  const [calls, setCalls] = useState<Call[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCalls = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getCalls();
      if (response.success && response.data) {
        // Sort calls by creation date (newest first)
        const sortedCalls = response.data.sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        setCalls(sortedCalls);
      } else {
        setError(response.error || 'Failed to fetch calls');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch calls');
    } finally {
      setLoading(false);
    }
  }, []);

  const getCall = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getCall(id);
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get call');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get call';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const createCall = useCallback(async (call: CreateCallRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.createCall(call);
      if (response.success && response.data) {
        setCalls(prev => [response.data!, ...prev]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create call');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create call';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const createPhoneCall = useCallback(async (call: CreateCallRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.createPhoneCall(call);
      if (response.success && response.data) {
        setCalls(prev => [response.data!, ...prev]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create phone call');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create phone call';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const endCall = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.endCall(id);
      if (response.success) {
        // Update the call status in the local state
        setCalls(prev => 
          prev.map(call => 
            call.id === id 
              ? { ...call, status: 'ended' as const, endedAt: new Date().toISOString() }
              : call
          )
        );
        return true;
      } else {
        throw new Error(response.error || 'Failed to end call');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to end call';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Helper functions for filtering and formatting
  const getCallsByStatus = useCallback((status: Call['status']) => {
    return calls.filter(call => call.status === status);
  }, [calls]);

  const getCallsByType = useCallback((type: Call['type']) => {
    return calls.filter(call => call.type === type);
  }, [calls]);

  const getCallsByDateRange = useCallback((startDate: Date, endDate: Date) => {
    return calls.filter(call => {
      const callDate = new Date(call.createdAt);
      return callDate >= startDate && callDate <= endDate;
    });
  }, [calls]);

  const formatCallDuration = useCallback((call: Call) => {
    if (!call.startedAt || !call.endedAt) return 'N/A';
    
    const start = new Date(call.startedAt);
    const end = new Date(call.endedAt);
    const durationMs = end.getTime() - start.getTime();
    const durationSeconds = Math.floor(durationMs / 1000);
    
    const minutes = Math.floor(durationSeconds / 60);
    const seconds = durationSeconds % 60;
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  const getCallStatusColor = useCallback((status: Call['status']) => {
    switch (status) {
      case 'ended':
        return 'bg-green-500/20 text-green-500';
      case 'in-progress':
        return 'bg-blue-500/20 text-blue-500';
      case 'queued':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'ringing':
        return 'bg-purple-500/20 text-purple-500';
      case 'forwarding':
        return 'bg-orange-500/20 text-orange-500';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  }, []);

  useEffect(() => {
    fetchCalls();
  }, [fetchCalls]);

  return {
    calls,
    loading,
    error,
    fetchCalls,
    getCall,
    createCall,
    createPhoneCall,
    endCall,
    getCallsByStatus,
    getCallsByType,
    getCallsByDateRange,
    formatCallDuration,
    getCallStatusColor,
    clearError: () => setError(null),
  };
};
