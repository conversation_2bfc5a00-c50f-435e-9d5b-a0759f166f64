import React, { useState, useRef } from 'react';
import {
  Plus,
  Search,
  AlertCircle,
  Upload,
  FileText,
  Trash2,
  Edit,
  Download,
  Database,
  Brain,
  FileSearch,
  Shuffle,
  Eye,
  X,
  CheckCircle,
  Clock,
  Globe
} from 'lucide-react';
import { Modal } from '../components/Modal';
import { ApiKeySetup } from '../components/ApiKeySetup';
import { useFiles } from '../hooks/useFiles';
import { useKnowledgeBase } from '../hooks/useKnowledgeBase';
import { VapiFile, KnowledgeBase as KnowledgeBaseType, CreateKnowledgeBaseRequest } from '../services/api';

export const KnowledgeBase: React.FC = () => {
  // State management
  const [activeTab, setActiveTab] = useState<'files' | 'knowledge-bases'>('files');
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [isCreateKBModalOpen, setIsCreateKBModalOpen] = useState(false);
  const [isEditKBModalOpen, setIsEditKBModalOpen] = useState(false);
  const [selectedKB, setSelectedKB] = useState<KnowledgeBaseType | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [deleteType, setDeleteType] = useState<'file' | 'kb'>('file');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [hasApiKey, setHasApiKey] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Form state for knowledge base creation/editing
  const [kbName, setKbName] = useState('');
  const [kbProvider, setKbProvider] = useState<'trieve' | 'google'>('trieve');
  const [searchType, setSearchType] = useState<'semantic' | 'fulltext' | 'hybrid'>('semantic');
  const [topK, setTopK] = useState(5);
  const [scoreThreshold, setScoreThreshold] = useState(0.7);
  const [removeStopWords, setRemoveStopWords] = useState(true);
  const [websites, setWebsites] = useState<string[]>(['']);

  // Check for API key on mount
  React.useEffect(() => {
    const apiKey = import.meta.env.VITE_VAPI_API_KEY || localStorage.getItem('vapi_api_key');
    setHasApiKey(!!apiKey);
  }, []);

  // Hooks
  const {
    files,
    loading: filesLoading,
    error: filesError,
    uploadProgress,
    uploadFile,
    deleteFile,
    formatFileSize,
    getFileIcon,
    getFileTypeColor,
    isValidFileType,
    clearError: clearFilesError,
  } = useFiles();

  const {
    knowledgeBases,
    loading: kbLoading,
    error: kbError,
    createKnowledgeBase,
    updateKnowledgeBase,
    deleteKnowledgeBase,
    getProviderIcon,
    getProviderColor,
    getSearchTypeIcon,
    getSearchTypeColor,
    formatKnowledgeBaseName,
    getFileCount,
    getWebsiteCount,
    createDefaultKnowledgeBase,
    validateKnowledgeBase,
    clearError: clearKBError,
  } = useKnowledgeBase();

  // Filter files and knowledge bases based on search
  const filteredFiles = files.filter(file =>
    !searchQuery ||
    file.originalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.mimetype.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.id.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredKnowledgeBases = knowledgeBases.filter(kb =>
    !searchQuery ||
    formatKnowledgeBaseName(kb).toLowerCase().includes(searchQuery.toLowerCase()) ||
    kb.provider.toLowerCase().includes(searchQuery.toLowerCase()) ||
    kb.id.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Reset form state
  const resetKBForm = () => {
    setKbName('');
    setKbProvider('trieve');
    setSearchType('semantic');
    setTopK(5);
    setScoreThreshold(0.7);
    setRemoveStopWords(true);
    setSelectedFiles([]);
    setWebsites(['']);
  };

  // Handle file upload
  const handleFileUpload = async (uploadedFiles: FileList) => {
    const validFiles = Array.from(uploadedFiles).filter(isValidFileType);

    if (validFiles.length === 0) {
      alert('Please select valid file types (PDF, TXT, DOCX, etc.)');
      return;
    }

    try {
      for (const file of validFiles) {
        await uploadFile(file);
      }
      setIsUploadModalOpen(false);
    } catch (err) {
      console.error('Failed to upload files:', err);
    }
  };

  // Handle drag and drop
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFileUpload(droppedFiles);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  // Show API key setup if no key is found
  if (!hasApiKey) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0F0F0F] text-white p-6">
        <ApiKeySetup onApiKeySet={() => setHasApiKey(true)} />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold mb-1">Knowledge Base</h1>
            <p className="text-gray-400">Manage files and knowledge bases for your AI agents</p>
          </div>
          <div className="flex gap-3">
            {activeTab === 'files' && (
              <button
                onClick={() => setIsUploadModalOpen(true)}
                className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
              >
                <Upload size={16} />
                Upload Files
              </button>
            )}
            {activeTab === 'knowledge-bases' && (
              <button
                onClick={() => setIsCreateKBModalOpen(true)}
                className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
              >
                <Plus size={16} />
                Create Knowledge Base
              </button>
            )}
          </div>
        </div>

        {/* Error Display */}
        {(filesError || kbError) && (
          <div className="mb-6 p-4 bg-red-900/20 border border-red-800 rounded-lg flex items-center gap-2">
            <AlertCircle size={18} className="text-red-400" />
            <span className="text-red-400">{filesError || kbError}</span>
            <button
              onClick={() => {
                clearFilesError();
                clearKBError();
              }}
              className="ml-auto text-red-400 hover:text-red-300"
            >
              ×
            </button>
          </div>
        )}

        {/* Tabs */}
        <div className="flex border-b border-gray-800 mb-6">
          <button
            onClick={() => setActiveTab('files')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'files'
                ? 'border-white text-white'
                : 'border-transparent text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center gap-2">
              <FileText size={16} />
              Files ({files.length})
            </div>
          </button>
          <button
            onClick={() => setActiveTab('knowledge-bases')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'knowledge-bases'
                ? 'border-white text-white'
                : 'border-transparent text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center gap-2">
              <Database size={16} />
              Knowledge Bases ({knowledgeBases.length})
            </div>
          </button>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder={`Search ${activeTab === 'files' ? 'files' : 'knowledge bases'}...`}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
          />
        </div>

        {/* Content */}
        {activeTab === 'files' ? (
          /* Files Tab */
          (filesLoading && filteredFiles.length === 0) ? (
            <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-8 text-center">
              <div className="animate-spin w-6 h-6 border-2 border-gray-600 border-t-white rounded-full mx-auto mb-2"></div>
              <p className="text-gray-400">Loading files...</p>
            </div>
          ) : filteredFiles.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredFiles.map((file) => (
                <div
                  key={file.id}
                  className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-4 hover:border-gray-700 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <span className="text-xl">{getFileIcon(file.mimetype)}</span>
                      <span className={`px-2 py-0.5 rounded-full text-xs ${getFileTypeColor(file.mimetype)}`}>
                        {file.mimetype.split('/')[1]?.toUpperCase() || 'FILE'}
                      </span>
                    </div>
                    <div className="flex gap-1">
                      <button
                        className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                        title="Download file"
                        onClick={() => window.open(file.url, '_blank')}
                      >
                        <Download size={14} className="text-gray-400" />
                      </button>
                      <button
                        className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                        onClick={() => {
                          setShowDeleteConfirm(file.id);
                          setDeleteType('file');
                        }}
                        title="Delete file"
                      >
                        <Trash2 size={14} className="text-red-400" />
                      </button>
                    </div>
                  </div>

                  <h3 className="font-medium text-gray-200 mb-1 truncate" title={file.originalName}>
                    {file.originalName}
                  </h3>

                  <p className="text-sm text-gray-400 mb-3">
                    {formatFileSize(file.bytes)}
                  </p>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Uploaded {formatDate(file.createdAt)}</span>
                    <span className="text-gray-600">ID: {file.id.slice(0, 8)}...</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            /* Empty state for files */
            <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-12 text-center">
              <div className="w-12 h-12 bg-[#252525] rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileText size={24} className="text-gray-400" />
              </div>
              <h3 className="text-lg font-medium mb-2">
                {searchQuery ? 'No files found' : 'No files uploaded'}
              </h3>
              <p className="text-sm text-gray-400 mb-6">
                {searchQuery
                  ? 'No files match your search criteria.'
                  : 'Upload files to create knowledge bases for your AI agents.'
                }
              </p>
              {!searchQuery && (
                <button
                  onClick={() => setIsUploadModalOpen(true)}
                  className="px-4 py-2 bg-[#252525] text-white rounded-lg text-sm hover:bg-[#333333] transition-colors inline-flex items-center gap-2"
                >
                  <Upload size={16} />
                  Upload your first file
                </button>
              )}
            </div>
          )
        ) : (
          /* Knowledge Bases Tab */
          (kbLoading && filteredKnowledgeBases.length === 0) ? (
            <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-8 text-center">
              <div className="animate-spin w-6 h-6 border-2 border-gray-600 border-t-white rounded-full mx-auto mb-2"></div>
              <p className="text-gray-400">Loading knowledge bases...</p>
            </div>
          ) : filteredKnowledgeBases.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredKnowledgeBases.map((kb) => (
                <div
                  key={kb.id}
                  className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-4 hover:border-gray-700 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <span className="text-xl">{getProviderIcon(kb.provider)}</span>
                      <span className={`px-2 py-0.5 rounded-full text-xs capitalize ${getProviderColor(kb.provider)}`}>
                        {kb.provider}
                      </span>
                    </div>
                    <div className="flex gap-1">
                      <button
                        className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                        onClick={() => {
                          setSelectedKB(kb);
                          setIsEditKBModalOpen(true);
                        }}
                        title="Edit knowledge base"
                      >
                        <Edit size={14} className="text-gray-400" />
                      </button>
                      <button
                        className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                        onClick={() => {
                          setShowDeleteConfirm(kb.id);
                          setDeleteType('kb');
                        }}
                        title="Delete knowledge base"
                      >
                        <Trash2 size={14} className="text-red-400" />
                      </button>
                    </div>
                  </div>

                  <h3 className="font-medium text-gray-200 mb-1">
                    {formatKnowledgeBaseName(kb)}
                  </h3>

                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-xl">{getSearchTypeIcon(kb.searchPlan.searchType)}</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs capitalize ${getSearchTypeColor(kb.searchPlan.searchType)}`}>
                      {kb.searchPlan.searchType}
                    </span>
                  </div>

                  <div className="space-y-1 mb-3">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>Files: {getFileCount(kb)}</span>
                      <span>Websites: {getWebsiteCount(kb)}</span>
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>Top K: {kb.searchPlan.topK || 5}</span>
                      <span>Threshold: {kb.searchPlan.scoreThreshold || 0.7}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Created {formatDate(kb.createdAt)}</span>
                    <span className="text-gray-600">ID: {kb.id.slice(0, 8)}...</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            /* Empty state for knowledge bases */
            <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-12 text-center">
              <div className="w-12 h-12 bg-[#252525] rounded-lg flex items-center justify-center mx-auto mb-4">
                <Database size={24} className="text-gray-400" />
              </div>
              <h3 className="text-lg font-medium mb-2">
                {searchQuery ? 'No knowledge bases found' : 'No knowledge bases created'}
              </h3>
              <p className="text-sm text-gray-400 mb-6">
                {searchQuery
                  ? 'No knowledge bases match your search criteria.'
                  : 'Create knowledge bases to give your AI agents domain-specific knowledge.'
                }
              </p>
              {!searchQuery && (
                <button
                  onClick={() => setIsCreateKBModalOpen(true)}
                  className="px-4 py-2 bg-[#252525] text-white rounded-lg text-sm hover:bg-[#333333] transition-colors inline-flex items-center gap-2"
                >
                  <Plus size={16} />
                  Create your first knowledge base
                </button>
              )}
            </div>
          )
        )}
      </div>

      {/* Upload Files Modal */}
      <Modal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        title="Upload Files"
      >
        <div
          className="text-center py-12 border-2 border-dashed border-gray-800 rounded-lg hover:border-gray-700 transition-colors cursor-pointer"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.txt,.docx,.doc,.md,.csv,.json,.html"
            onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
            className="hidden"
          />

          {uploadProgress > 0 ? (
            <div className="space-y-4">
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto">
                <Upload size={24} className="text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-300 mb-2">Uploading files...</p>
                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{uploadProgress}%</p>
              </div>
            </div>
          ) : (
            <div>
              <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Upload size={24} className="text-gray-400" />
              </div>
              <p className="text-sm text-gray-400 mb-1">Click or drag files to upload</p>
              <p className="text-xs text-gray-600 mb-4">Supported formats: PDF, TXT, DOCX, MD, CSV, JSON, HTML</p>
              <div className="flex justify-center gap-2 text-xs text-gray-600">
                <span className="px-2 py-1 bg-gray-800 rounded">pdf</span>
                <span className="px-2 py-1 bg-gray-800 rounded">txt</span>
                <span className="px-2 py-1 bg-gray-800 rounded">docx</span>
                <span className="px-2 py-1 bg-gray-800 rounded">md</span>
                <span className="px-2 py-1 bg-gray-800 rounded">csv</span>
              </div>
            </div>
          )}
        </div>
      </Modal>

      {/* Create Knowledge Base Modal */}
      <Modal
        isOpen={isCreateKBModalOpen}
        onClose={() => {
          setIsCreateKBModalOpen(false);
          resetKBForm();
        }}
        title="Create Knowledge Base"
      >
        <form onSubmit={async (e) => {
          e.preventDefault();
          if (!kbName.trim()) return;

          try {
            const kbData = createDefaultKnowledgeBase(kbName, selectedFiles);
            kbData.provider = kbProvider;
            kbData.searchPlan.searchType = searchType;
            kbData.searchPlan.topK = topK;
            kbData.searchPlan.scoreThreshold = scoreThreshold;
            kbData.searchPlan.removeStopWords = removeStopWords;

            // Add websites if provided
            if (websites.some(url => url.trim())) {
              const validWebsites = websites.filter(url => url.trim());
              if (kbData.createPlan?.chunkPlans?.[0]) {
                kbData.createPlan.chunkPlans[0].websites = validWebsites;
              }
            }

            await createKnowledgeBase(kbData);
            setIsCreateKBModalOpen(false);
            resetKBForm();
          } catch (err) {
            console.error('Failed to create knowledge base:', err);
          }
        }} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Name *
            </label>
            <input
              type="text"
              value={kbName}
              onChange={(e) => setKbName(e.target.value)}
              placeholder="e.g., Product Documentation"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Provider
            </label>
            <select
              value={kbProvider}
              onChange={(e) => setKbProvider(e.target.value as 'trieve' | 'google')}
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm focus:outline-none focus:border-gray-700 transition-colors"
            >
              <option value="trieve">Trieve</option>
              <option value="google">Google</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Search Type
            </label>
            <select
              value={searchType}
              onChange={(e) => setSearchType(e.target.value as 'semantic' | 'fulltext' | 'hybrid')}
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm focus:outline-none focus:border-gray-700 transition-colors"
            >
              <option value="semantic">Semantic Search</option>
              <option value="fulltext">Full Text Search</option>
              <option value="hybrid">Hybrid Search</option>
            </select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Top K Results
              </label>
              <input
                type="number"
                value={topK}
                onChange={(e) => setTopK(parseInt(e.target.value) || 5)}
                min="1"
                max="20"
                className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm focus:outline-none focus:border-gray-700 transition-colors"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Score Threshold
              </label>
              <input
                type="number"
                value={scoreThreshold}
                onChange={(e) => setScoreThreshold(parseFloat(e.target.value) || 0.7)}
                min="0"
                max="1"
                step="0.1"
                className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm focus:outline-none focus:border-gray-700 transition-colors"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="removeStopWords"
              checked={removeStopWords}
              onChange={(e) => setRemoveStopWords(e.target.checked)}
              className="rounded border-gray-800 bg-[#0F0F0F] text-white focus:ring-gray-700"
            />
            <label htmlFor="removeStopWords" className="text-sm text-gray-300">
              Remove stop words
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Select Files (Optional)
            </label>
            <div className="max-h-32 overflow-y-auto border border-gray-800 rounded-lg p-2 bg-[#0F0F0F]">
              {files.length > 0 ? (
                files.map((file) => (
                  <div key={file.id} className="flex items-center gap-2 py-1">
                    <input
                      type="checkbox"
                      id={`file-${file.id}`}
                      checked={selectedFiles.includes(file.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedFiles(prev => [...prev, file.id]);
                        } else {
                          setSelectedFiles(prev => prev.filter(id => id !== file.id));
                        }
                      }}
                      className="rounded border-gray-800 bg-[#0F0F0F] text-white focus:ring-gray-700"
                    />
                    <label htmlFor={`file-${file.id}`} className="text-sm text-gray-300 truncate">
                      {file.originalName}
                    </label>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No files available. Upload files first.</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Websites (Optional)
            </label>
            {websites.map((website, index) => (
              <div key={index} className="flex gap-2 mb-2">
                <input
                  type="url"
                  value={website}
                  onChange={(e) => {
                    const newWebsites = [...websites];
                    newWebsites[index] = e.target.value;
                    setWebsites(newWebsites);
                  }}
                  placeholder="https://example.com"
                  className="flex-1 bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
                />
                {websites.length > 1 && (
                  <button
                    type="button"
                    onClick={() => setWebsites(websites.filter((_, i) => i !== index))}
                    className="p-2 text-red-400 hover:bg-red-900/20 rounded-lg transition-colors"
                  >
                    <X size={16} />
                  </button>
                )}
              </div>
            ))}
            <button
              type="button"
              onClick={() => setWebsites([...websites, ''])}
              className="text-sm text-gray-400 hover:text-white transition-colors flex items-center gap-1"
            >
              <Plus size={14} />
              Add website
            </button>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setIsCreateKBModalOpen(false);
                resetKBForm();
              }}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={kbLoading || !kbName.trim()}
              className="px-4 py-2 bg-white text-black text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {kbLoading ? 'Creating...' : 'Create Knowledge Base'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(null)}
        title={`Delete ${deleteType === 'file' ? 'File' : 'Knowledge Base'}`}
      >
        <div className="space-y-4">
          <p className="text-gray-300">
            Are you sure you want to delete this {deleteType === 'file' ? 'file' : 'knowledge base'}?
            This action cannot be undone.
          </p>
          <div className="flex justify-end gap-3">
            <button
              onClick={() => setShowDeleteConfirm(null)}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={async () => {
                if (!showDeleteConfirm) return;
                try {
                  if (deleteType === 'file') {
                    await deleteFile(showDeleteConfirm);
                  } else {
                    await deleteKnowledgeBase(showDeleteConfirm);
                  }
                  setShowDeleteConfirm(null);
                } catch (err) {
                  console.error(`Failed to delete ${deleteType}:`, err);
                }
              }}
              disabled={filesLoading || kbLoading}
              className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {(filesLoading || kbLoading) ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};