import { useState, useEffect, useCallback } from 'react';
import { apiClient, PhoneNumber, CreatePhoneNumberRequest } from '../services/api';

export const usePhoneNumbers = () => {
  const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPhoneNumbers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getPhoneNumbers();
      if (response.success && response.data) {
        setPhoneNumbers(response.data);
      } else {
        setError(response.error || 'Failed to fetch phone numbers');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch phone numbers');
    } finally {
      setLoading(false);
    }
  }, []);

  const createPhoneNumber = useCallback(async (phoneNumber: CreatePhoneNumberRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.createPhoneNumber(phoneNumber);
      if (response.success && response.data) {
        setPhoneNumbers(prev => [...prev, response.data!]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create phone number');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create phone number';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const updatePhoneNumber = useCallback(async (id: string, updates: Partial<CreatePhoneNumberRequest>) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.updatePhoneNumber(id, updates);
      if (response.success && response.data) {
        setPhoneNumbers(prev => 
          prev.map(phoneNumber => 
            phoneNumber.id === id ? response.data! : phoneNumber
          )
        );
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to update phone number');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update phone number';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deletePhoneNumber = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.deletePhoneNumber(id);
      if (response.success) {
        setPhoneNumbers(prev => prev.filter(phoneNumber => phoneNumber.id !== id));
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete phone number');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete phone number';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const getPhoneNumber = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getPhoneNumber(id);
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get phone number');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get phone number';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPhoneNumbers();
  }, [fetchPhoneNumbers]);

  return {
    phoneNumbers,
    loading,
    error,
    fetchPhoneNumbers,
    createPhoneNumber,
    updatePhoneNumber,
    deletePhoneNumber,
    getPhoneNumber,
    clearError: () => setError(null),
  };
};
