import React from 'react';
import { Play, Pause, RotateCcw, Download, X, ChevronRight } from 'lucide-react';
import { Call } from '../../services/api';

interface CallDetailsProps {
  call: Call;
  onClose: () => void;
}

export const CallDetails: React.FC<CallDetailsProps> = ({ call, onClose }) => {
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [currentTime, setCurrentTime] = React.useState(0);
  const [activeTab, setActiveTab] = React.useState<'overview' | 'transcription' | 'client-data'>('overview');

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  // Calculate call duration
  const getCallDuration = () => {
    if (!call.startedAt || !call.endedAt) return 'N/A';

    const start = new Date(call.startedAt);
    const end = new Date(call.endedAt);
    const durationMs = end.getTime() - start.getTime();
    const durationSeconds = Math.floor(durationMs / 1000);

    const minutes = Math.floor(durationSeconds / 60);
    const seconds = durationSeconds % 60;

    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Get status color
  const getStatusColor = () => {
    switch (call.status) {
      case 'ended':
        return 'bg-green-500/20 text-green-500';
      case 'in-progress':
        return 'bg-blue-500/20 text-blue-500';
      case 'queued':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'ringing':
        return 'bg-purple-500/20 text-purple-500';
      case 'forwarding':
        return 'bg-orange-500/20 text-orange-500';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50">
      <div className="absolute inset-y-0 right-0 w-[480px] bg-[#1A1A1A] shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          <div>
            <h2 className="text-sm font-medium mb-1">
              Call with {call.customer?.name || call.customer?.number || 'Unknown'}
            </h2>
            <div className="text-xs text-gray-400">{call.id}</div>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-800 rounded-lg transition-colors"
          >
            <X size={20} className="text-gray-400" />
          </button>
        </div>

        {/* Audio Player */}
        {call.recordingUrl ? (
          <div className="p-4 border-b border-gray-800">
            <div className="flex items-center gap-4 mb-4">
              <button
                onClick={togglePlayback}
                className="w-10 h-10 rounded-full bg-white text-black flex items-center justify-center hover:bg-gray-100 transition-colors"
              >
                {isPlaying ? <Pause size={20} /> : <Play size={20} />}
              </button>
              <div className="flex-1">
                <div className="h-1 bg-gray-800 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-white rounded-full"
                    style={{ width: `${(currentTime / 143) * 100}%` }}
                  />
                </div>
                <div className="flex justify-between mt-1 text-xs text-gray-400">
                  <span>0:00</span>
                  <span>{getCallDuration()}</span>
                </div>
              </div>
              <button className="p-2 hover:bg-gray-800 rounded-lg transition-colors">
                <RotateCcw size={20} className="text-gray-400" />
              </button>
              <a
                href={call.recordingUrl}
                download
                className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
              >
                <Download size={20} className="text-gray-400" />
              </a>
            </div>
          </div>
        ) : (
          <div className="p-4 border-b border-gray-800">
            <div className="text-center text-gray-400 text-sm">
              No recording available for this call
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="flex border-b border-gray-800">
          <button
            onClick={() => setActiveTab('overview')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'overview'
                ? 'border-white text-white'
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('transcription')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'transcription'
                ? 'border-white text-white'
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
          >
            Transcription
          </button>
          <button
            onClick={() => setActiveTab('client-data')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'client-data'
                ? 'border-white text-white'
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
          >
            Client data
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="p-4 space-y-6">
              {call.summary && (
                <div>
                  <h3 className="text-sm font-medium mb-2">Summary</h3>
                  <p className="text-sm text-gray-400">{call.summary}</p>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium mb-2">Call status</h3>
                <span className={`inline-flex px-2 py-1 rounded-full text-xs capitalize ${getStatusColor()}`}>
                  {call.status}
                </span>
                {call.endedReason && (
                  <div className="mt-2 text-xs text-gray-400">
                    Ended reason: {call.endedReason}
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Call Details</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Type</span>
                    <span className="capitalize">{call.type.replace('PhoneCall', '').replace('Call', '')}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Created</span>
                    <span>{formatDate(call.createdAt)}, {formatTime(call.createdAt)}</span>
                  </div>
                  {call.startedAt && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Started</span>
                      <span>{formatDate(call.startedAt)}, {formatTime(call.startedAt)}</span>
                    </div>
                  )}
                  {call.endedAt && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Ended</span>
                      <span>{formatDate(call.endedAt)}, {formatTime(call.endedAt)}</span>
                    </div>
                  )}
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Duration</span>
                    <span>{getCallDuration()}</span>
                  </div>
                  {call.phoneCallProvider && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Provider</span>
                      <span className="capitalize">{call.phoneCallProvider}</span>
                    </div>
                  )}
                </div>
              </div>

              {call.cost !== undefined && (
                <div>
                  <h3 className="text-sm font-medium mb-2">Cost Breakdown</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Total Cost</span>
                      <span>${call.cost.toFixed(4)}</span>
                    </div>
                    {call.costBreakdown && (
                      <>
                        {call.costBreakdown.transport !== undefined && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-400">Transport</span>
                            <span>${call.costBreakdown.transport.toFixed(4)}</span>
                          </div>
                        )}
                        {call.costBreakdown.stt !== undefined && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-400">Speech-to-Text</span>
                            <span>${call.costBreakdown.stt.toFixed(4)}</span>
                          </div>
                        )}
                        {call.costBreakdown.llm !== undefined && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-400">LLM</span>
                            <span>${call.costBreakdown.llm.toFixed(4)}</span>
                          </div>
                        )}
                        {call.costBreakdown.tts !== undefined && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-400">Text-to-Speech</span>
                            <span>${call.costBreakdown.tts.toFixed(4)}</span>
                          </div>
                        )}
                        {call.costBreakdown.vapi !== undefined && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-400">Vapi</span>
                            <span>${call.costBreakdown.vapi.toFixed(4)}</span>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'transcription' && (
            <div className="p-4">
              {call.messages && call.messages.length > 0 ? (
                <div className="space-y-4">
                  {call.messages.map((message, index) => (
                    <div key={index} className="flex gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                        message.role === 'assistant'
                          ? 'bg-blue-500 text-white'
                          : message.role === 'user'
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-500 text-white'
                      }`}>
                        {message.role === 'assistant' ? 'A' : message.role === 'user' ? 'U' : 'S'}
                      </div>
                      <div className="flex-1">
                        <div className="text-sm">
                          <span className="font-medium capitalize">{message.role}</span>
                          {message.time !== undefined && (
                            <span className="text-gray-400 ml-2">
                              {Math.floor(message.time / 60)}:{(message.time % 60).toString().padStart(2, '0')}
                            </span>
                          )}
                          {message.secondsFromStart !== undefined && (
                            <span className="text-gray-400 ml-2">
                              {Math.floor(message.secondsFromStart / 60)}:{(message.secondsFromStart % 60).toString().padStart(2, '0')}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-400 mt-1">
                          {message.message || message.content || 'No content'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : call.transcript ? (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Transcript</h3>
                  <div className="text-sm text-gray-400 whitespace-pre-wrap">
                    {call.transcript}
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-400 text-sm py-8">
                  No transcription available for this call
                </div>
              )}
            </div>
          )}

          {activeTab === 'client-data' && (
            <div className="p-4">
              {call.customer ? (
                <div className="bg-[#0F0F0F] rounded-lg border border-gray-800 divide-y divide-gray-800">
                  {call.customer.name && (
                    <div className="p-3">
                      <div className="text-sm text-gray-400 mb-1">Name</div>
                      <div className="text-sm">{call.customer.name}</div>
                    </div>
                  )}
                  {call.customer.number && (
                    <div className="p-3">
                      <div className="text-sm text-gray-400 mb-1">Phone</div>
                      <div className="text-sm">{call.customer.number}</div>
                    </div>
                  )}
                  {call.customer.email && (
                    <div className="p-3">
                      <div className="text-sm text-gray-400 mb-1">Email</div>
                      <div className="text-sm">{call.customer.email}</div>
                    </div>
                  )}
                  {call.type && (
                    <div className="p-3">
                      <div className="text-sm text-gray-400 mb-1">Call Type</div>
                      <div className="text-sm capitalize">{call.type.replace('PhoneCall', '').replace('Call', '')}</div>
                    </div>
                  )}
                  {call.phoneCallProvider && (
                    <div className="p-3">
                      <div className="text-sm text-gray-400 mb-1">Provider</div>
                      <div className="text-sm capitalize">{call.phoneCallProvider}</div>
                    </div>
                  )}
                  {call.phoneCallTransport && (
                    <div className="p-3">
                      <div className="text-sm text-gray-400 mb-1">Transport</div>
                      <div className="text-sm capitalize">{call.phoneCallTransport}</div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-gray-400 text-sm py-8">
                  No customer data available for this call
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};