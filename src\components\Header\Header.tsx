import React from 'react';
import { useLocation } from 'react-router-dom';
import { Breadcrumb } from './Breadcrumb';
import { X } from 'lucide-react';

interface HeaderProps {
  currentPage: string;
}

export const Header: React.FC<HeaderProps> = () => {
  const location = useLocation();
  const pathSegments = location.pathname.split('/').filter(Boolean);

  const getBreadcrumbItems = () => {
    const items = [];
    let path = '';

    // Always start with App
    items.push({
      label: 'App',
      link: '/',
      active: false
    });

    // Add remaining segments, skipping 'app' and 'conversational-ai'
    for (const segment of pathSegments) {
      if (segment !== 'app') {
        path += `/${segment}`;
        const label = segment.split('-').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');

        items.push({
          label,
          link: `/app${path}`,
          active: `/app${path}` === location.pathname
        });
      }
    }

    return items;
  };

  return (
    <header className="h-14 border-b border-gray-800 flex items-center justify-between px-6">
      <Breadcrumb items={getBreadcrumbItems()} />
      <button className="p-1 text-gray-400 hover:text-white transition-colors">
        <X size={20} />
      </button>
    </header>
  );
};