import { Router, Request, Response } from 'express';
import { vapiServices } from '../../api/services/index.js';
import { CreateCallRequest, CallListQuery } from '../../api/types.js';

const router = Router();

/**
 * POST /api/calls/start
 * Start an outbound call
 */
router.post('/start', async (req: Request, res: Response) => {
  try {
    const callRequest: CreateCallRequest = req.body;
    const result = await vapiServices.calls.createCall(callRequest);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    console.error('Error starting call:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start call',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/calls
 * List all calls with optional filtering
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const query: CallListQuery = {
      page: req.query.page ? parseInt(req.query.page as string) : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      assistantId: req.query.assistantId as string,
      phoneNumberId: req.query.phoneNumberId as string,
      status: req.query.status as string,
      createdAtGte: req.query.createdAtGte as string,
      createdAtLte: req.query.createdAtLte as string,
    };

    const result = await vapiServices.calls.listCalls(query);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error listing calls:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to list calls',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/calls/:id
 * Get call details/status
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.calls.getCall(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting call:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get call',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/calls/:id/end
 * End a live call early
 */
router.post('/:id/end', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.calls.endCall(id);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error ending call:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to end call',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/calls/:id/transcript
 * Fetch full transcript
 */
router.get('/:id/transcript', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.calls.getCallTranscript(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting call transcript:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get call transcript',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/calls/:id/audio
 * Stream or download recording
 */
router.get('/:id/audio', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.calls.getCallAudio(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting call audio:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get call audio',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * PUT /api/calls/:id
 * Update call details
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    const result = await vapiServices.calls.updateCall(id, updates);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error updating call:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update call',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * DELETE /api/calls/:id
 * Delete call
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.calls.deleteCall(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error deleting call:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete call',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/calls/:id/stats
 * Get call analytics/stats
 */
router.get('/:id/stats', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.calls.getCallStats(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting call stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get call stats',
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
