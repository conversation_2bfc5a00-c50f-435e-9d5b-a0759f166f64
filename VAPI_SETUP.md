# Vapi Voice Integration Setup

This project includes full voice conversation capabilities using Vapi.ai. Follow these steps to set up voice testing with your AI agents.

## Prerequisites

1. **Vapi Account**: Sign up at [https://dashboard.vapi.ai](https://dashboard.vapi.ai)
2. **AI Agents**: Create at least one assistant in your Vapi dashboard

## Setup Instructions

### 1. Get Your Vapi Public Key

1. Go to [Vapi Dashboard](https://dashboard.vapi.ai)
2. Navigate to **Settings** → **API Keys**
3. Copy your **Public Key** (starts with `pk_`)

### 2. Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your Vapi public key:
   ```env
   VITE_VAPI_PUBLIC_KEY=pk_your_actual_public_key_here
   ```

### 3. Test Voice Calls

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to the **Agents** page
3. Click the **green phone icon** next to any agent
4. Click the **green phone button** to start a voice call
5. Allow microphone access when prompted
6. Speak to test the conversation

## Features

- **Real-time voice conversations** with your AI agents
- **3D orb visualization** that reacts to speech
- **Visual feedback** for listening/speaking states
- **Error handling** with clear user messages
- **Browser-based** - no additional software needed

## Troubleshooting

### "Failed to start call" Error
- Ensure your `VITE_VAPI_PUBLIC_KEY` is correctly set
- Check that the agent ID exists in your Vapi dashboard
- Verify microphone permissions are granted

### "Voice system not initialized" Error
- Check browser console for detailed error messages
- Ensure you're using a supported browser (Chrome, Edge, Safari)
- Verify your Vapi public key is valid

### No Audio
- Check microphone permissions in browser settings
- Ensure speakers/headphones are working
- Try refreshing the page and starting a new call

## Browser Compatibility

- ✅ Chrome (recommended)
- ✅ Edge
- ✅ Safari
- ❌ Firefox (limited Web Audio API support)

## Next Steps

- Customize agent prompts in the Vapi dashboard
- Add custom tools and integrations
- Configure voice settings and providers
- Set up phone number integration for external calls
