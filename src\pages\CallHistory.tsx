import React, { useState, useMemo } from 'react';
import { Search, ChevronRight, AlertCircle, Calendar, Filter, Phone, PhoneCall } from 'lucide-react';
import { CallDetails } from '../components/CallDetails';
import { ApiKeySetup } from '../components/ApiKeySetup';
import { useCalls } from '../hooks/useCalls';
import { useAssistants } from '../hooks/useAssistants';
import { Call } from '../services/api';

export const CallHistory: React.FC = () => {
  const [selectedCall, setSelectedCall] = useState<Call | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [hasApiKey, setHasApiKey] = useState(false);

  // Check for API key on mount
  React.useEffect(() => {
    const apiKey = import.meta.env.VITE_VAPI_API_KEY || localStorage.getItem('vapi_api_key');
    setHasApiKey(!!apiKey);
  }, []);

  const {
    calls,
    loading,
    error,
    formatCallDuration,
    getCallStatusColor,
    clearError,
  } = useCalls();

  const { assistants } = useAssistants();

  // Filter calls based on search query and filters
  const filteredCalls = useMemo(() => {
    return calls.filter(call => {
      // Search filter
      const matchesSearch = !searchQuery ||
        call.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        call.customer?.number?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        call.summary?.toLowerCase().includes(searchQuery.toLowerCase());

      // Status filter
      const matchesStatus = statusFilter === 'all' || call.status === statusFilter;

      // Type filter
      const matchesType = typeFilter === 'all' || call.type === typeFilter;

      return matchesSearch && matchesStatus && matchesType;
    });
  }, [calls, searchQuery, statusFilter, typeFilter]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  // Get assistant name by ID
  const getAssistantName = (assistantId?: string) => {
    if (!assistantId) return 'No assistant';
    const assistant = assistants.find(a => a.id === assistantId);
    return assistant?.name || 'Unknown Assistant';
  };

  // Get call type icon
  const getCallTypeIcon = (type: Call['type']) => {
    switch (type) {
      case 'inboundPhoneCall':
        return <Phone size={16} className="text-green-400" />;
      case 'outboundPhoneCall':
        return <PhoneCall size={16} className="text-blue-400" />;
      case 'webCall':
        return <Phone size={16} className="text-purple-400" />;
      default:
        return <Phone size={16} className="text-gray-400" />;
    }
  };

  // Show API key setup if no key is found
  if (!hasApiKey) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0F0F0F] text-white p-6">
        <ApiKeySetup onApiKeySet={() => setHasApiKey(true)} />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-semibold mb-1">Call History</h1>
          <p className="text-gray-400">View and analyze your Vapi call history</p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-900/20 border border-red-800 rounded-lg flex items-center gap-2">
            <AlertCircle size={18} className="text-red-400" />
            <span className="text-red-400">{error}</span>
            <button
              onClick={clearError}
              className="ml-auto text-red-400 hover:text-red-300"
            >
              ×
            </button>
          </div>
        )}

        {/* Filters */}
        <div className="flex flex-wrap gap-2 mb-6">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-1 bg-[#1A1A1A] border border-gray-800 rounded-full text-sm text-gray-400 hover:bg-[#252525] transition-colors focus:outline-none focus:border-gray-700"
          >
            <option value="all">All statuses</option>
            <option value="ended">Ended</option>
            <option value="in-progress">In Progress</option>
            <option value="queued">Queued</option>
            <option value="ringing">Ringing</option>
            <option value="forwarding">Forwarding</option>
          </select>

          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-1 bg-[#1A1A1A] border border-gray-800 rounded-full text-sm text-gray-400 hover:bg-[#252525] transition-colors focus:outline-none focus:border-gray-700"
          >
            <option value="all">All types</option>
            <option value="inboundPhoneCall">Inbound</option>
            <option value="outboundPhoneCall">Outbound</option>
            <option value="webCall">Web Call</option>
          </select>

          <button className="px-3 py-1 bg-[#1A1A1A] rounded-full text-sm text-gray-400 hover:bg-[#252525] transition-colors flex items-center gap-1">
            <Calendar size={14} />
            Date filter
          </button>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search calls by ID, phone number, or summary..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
          />
        </div>

        {loading && filteredCalls.length === 0 ? (
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-8 text-center">
            <div className="animate-spin w-6 h-6 border-2 border-gray-600 border-t-white rounded-full mx-auto mb-2"></div>
            <p className="text-gray-400">Loading call history...</p>
          </div>
        ) : filteredCalls.length > 0 ? (
          /* Table */
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800">
            <div className="grid grid-cols-[200px,1fr,120px,100px,150px,40px] px-4 py-3 border-b border-gray-800">
              <div className="text-sm text-gray-400">Date & Time</div>
              <div className="text-sm text-gray-400">Assistant & Customer</div>
              <div className="text-sm text-gray-400">Duration</div>
              <div className="text-sm text-gray-400">Type</div>
              <div className="text-sm text-gray-400">Status</div>
              <div></div>
            </div>

            {filteredCalls.map((call) => (
              <div
                key={call.id}
                className="grid grid-cols-[200px,1fr,120px,100px,150px,40px] px-4 py-3 hover:bg-[#252525] transition-colors cursor-pointer group relative"
                onClick={() => setSelectedCall(call)}
              >
                <div className="text-sm">
                  <div className="text-gray-300">{formatDate(call.createdAt)}</div>
                  <div className="text-gray-400 text-xs">{formatTime(call.createdAt)}</div>
                </div>

                <div className="text-sm">
                  <div className="font-medium text-gray-300">{getAssistantName(call.assistantId)}</div>
                  <div className="text-gray-400 text-xs">
                    {call.customer?.number || call.customer?.name || 'Unknown customer'}
                  </div>
                </div>

                <div className="text-sm text-gray-400">
                  {formatCallDuration(call)}
                </div>

                <div className="flex items-center gap-2 text-sm">
                  {getCallTypeIcon(call.type)}
                  <span className="text-gray-400 capitalize">
                    {call.type.replace('PhoneCall', '').replace('Call', '')}
                  </span>
                </div>

                <div className="flex items-center">
                  <span className={`px-2 py-0.5 rounded-full text-xs capitalize ${getCallStatusColor(call.status)}`}>
                    {call.status}
                  </span>
                </div>

                <div className="flex justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <ChevronRight size={16} className="text-gray-400" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* Empty state */
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-12 text-center">
            <div className="w-12 h-12 bg-[#252525] rounded-lg flex items-center justify-center mx-auto mb-4">
              <Phone size={24} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
                ? 'No calls found'
                : 'No call history'
              }
            </h3>
            <p className="text-sm text-gray-400 mb-6">
              {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
                ? 'No calls match your search criteria or filters.'
                : 'You don\'t have any calls in your Vapi account yet.'
              }
            </p>
          </div>
        )}
      </div>

      {selectedCall && (
        <CallDetails
          call={selectedCall}
          onClose={() => setSelectedCall(null)}
        />
      )}
    </div>
  );
};