import React, { useState, useEffect } from 'react';
import { Key, AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';

interface ApiKeySetupProps {
  onApiKeySet?: () => void;
}

export const ApiKeySetup: React.FC<ApiKeySetupProps> = ({ onApiKeySet }) => {
  const [apiKey, setApiKey] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if API key is already stored
    const storedKey = localStorage.getItem('vapi_api_key');
    if (storedKey) {
      setApiKey(storedKey);
      setIsValid(true);
    }
  }, []);

  const validateApiKey = async (key: string) => {
    if (!key || !key.startsWith('sk-')) {
      setError('API key should start with "sk-"');
      setIsValid(false);
      return false;
    }

    setIsChecking(true);
    setError(null);

    try {
      const response = await fetch('https://api.vapi.ai/assistant', {
        headers: {
          'Authorization': `Bearer ${key}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setIsValid(true);
        localStorage.setItem('vapi_api_key', key);
        setError(null);
        onApiKeySet?.();
        return true;
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Invalid API key');
        setIsValid(false);
        return false;
      }
    } catch (err) {
      setError('Failed to validate API key. Please check your connection.');
      setIsValid(false);
      return false;
    } finally {
      setIsChecking(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await validateApiKey(apiKey);
  };

  const handleClear = () => {
    setApiKey('');
    setIsValid(false);
    setError(null);
    localStorage.removeItem('vapi_api_key');
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-[#1A1A1A] rounded-lg border border-gray-800">
      <div className="flex items-center gap-3 mb-4">
        <Key size={24} className="text-blue-400" />
        <h2 className="text-xl font-semibold text-white">Vapi API Key Setup</h2>
      </div>

      <p className="text-gray-400 text-sm mb-4">
        To connect to your Vapi account, please enter your API key. You can find this in your{' '}
        <a 
          href="https://dashboard.vapi.ai/account" 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-blue-400 hover:text-blue-300 inline-flex items-center gap-1"
        >
          Vapi Dashboard
          <ExternalLink size={12} />
        </a>
      </p>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="apiKey" className="block text-sm font-medium text-gray-300 mb-2">
            API Key
          </label>
          <div className="relative">
            <input
              type="password"
              id="apiKey"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="sk-..."
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 pr-10 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
              required
            />
            {isValid && (
              <CheckCircle size={18} className="absolute right-3 top-1/2 -translate-y-1/2 text-green-400" />
            )}
          </div>
        </div>

        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-900/20 border border-red-800 rounded-lg">
            <AlertCircle size={16} className="text-red-400" />
            <span className="text-red-400 text-sm">{error}</span>
          </div>
        )}

        {isValid && (
          <div className="flex items-center gap-2 p-3 bg-green-900/20 border border-green-800 rounded-lg">
            <CheckCircle size={16} className="text-green-400" />
            <span className="text-green-400 text-sm">API key is valid and saved!</span>
          </div>
        )}

        <div className="flex gap-3">
          <button
            type="submit"
            disabled={isChecking || !apiKey}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isChecking ? 'Validating...' : isValid ? 'Update Key' : 'Save Key'}
          </button>
          
          {isValid && (
            <button
              type="button"
              onClick={handleClear}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors"
            >
              Clear
            </button>
          )}
        </div>
      </form>

      <div className="mt-4 p-3 bg-blue-900/20 border border-blue-800 rounded-lg">
        <p className="text-blue-400 text-xs">
          <strong>Note:</strong> Your API key is stored locally in your browser and is only used to make requests to the Vapi API.
        </p>
      </div>
    </div>
  );
};
