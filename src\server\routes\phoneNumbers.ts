import { Router, Request, Response } from 'express';
import { vapiServices } from '../../api/services/index.js';
import { PhoneNumberListQuery, CreatePhoneNumberRequest } from '../../api/types.js';

const router = Router();

/**
 * GET /api/phone-numbers
 * List all purchased/assigned numbers
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const query: PhoneNumberListQuery = {
      page: req.query.page ? parseInt(req.query.page as string) : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      provider: req.query.provider as string,
      assignedToAssistant: req.query.assignedToAssistant === 'true' ? true :
                          req.query.assignedToAssistant === 'false' ? false : undefined,
    };

    const result = await vapiServices.phoneNumbers.listPhoneNumbers(query);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error listing phone numbers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to list phone numbers',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/phone-numbers/available
 * Get available phone numbers for purchase
 */
router.get('/available', async (req: Request, res: Response) => {
  try {
    const areaCode = req.query.areaCode as string;
    const country = req.query.country as string;

    const result = await vapiServices.phoneNumbers.getAvailableNumbers(areaCode, country);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting available phone numbers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get available phone numbers',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/phone-numbers
 * Purchase/create a new phone number
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const phoneNumberRequest: CreatePhoneNumberRequest = req.body;
    const result = await vapiServices.phoneNumbers.createPhoneNumber(phoneNumberRequest);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    console.error('Error creating phone number:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create phone number',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/phone-numbers/:id
 * Get phone number config
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.phoneNumbers.getPhoneNumber(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting phone number:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get phone number',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * PATCH /api/phone-numbers/:id
 * Assign an assistant or webhook
 */
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    const result = await vapiServices.phoneNumbers.updatePhoneNumber(id, updates);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error updating phone number:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update phone number',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * DELETE /api/phone-numbers/:id
 * Delete/release phone number
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.phoneNumbers.deletePhoneNumber(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error deleting phone number:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete phone number',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/phone-numbers/:id/assign-assistant
 * Assign an assistant to a phone number
 */
router.post('/:id/assign-assistant', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { assistantId } = req.body;

    if (!assistantId) {
      return res.status(400).json({
        success: false,
        error: 'assistantId is required',
        timestamp: new Date().toISOString(),
      });
    }

    const result = await vapiServices.phoneNumbers.assignAssistant(id, assistantId);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error assigning assistant to phone number:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to assign assistant to phone number',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/phone-numbers/:id/unassign-assistant
 * Unassign assistant from a phone number
 */
router.post('/:id/unassign-assistant', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.phoneNumbers.unassignAssistant(id);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error unassigning assistant from phone number:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to unassign assistant from phone number',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/phone-numbers/:id/usage
 * Get phone number usage statistics
 */
router.get('/:id/usage', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const dateRange = req.query.start && req.query.end ? {
      start: req.query.start as string,
      end: req.query.end as string,
    } : undefined;

    const result = await vapiServices.phoneNumbers.getPhoneNumberUsage(id, dateRange);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting phone number usage:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get phone number usage',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/phone-numbers/:id/configure
 * Configure phone number settings
 */
router.post('/:id/configure', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const config = req.body;

    const result = await vapiServices.phoneNumbers.configurePhoneNumber(id, config);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error configuring phone number:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to configure phone number',
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
