import React, { useMemo, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Plus, MoreHorizontal, Bot, Headphones, Users, CircleDollarSign, Trash2, Copy, AlertCircle, Phone, RefreshCw } from 'lucide-react';
import { Modal } from '../components/Modal';
import { ApiKeySetup } from '../components/ApiKeySetup';
import { useAssistants } from '../hooks/useAssistants';
import { Assistant, AssistantTemplate } from '../services/api';

// Debounce hook for search optimization
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = React.useState(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Memoized agent row component for performance
const AgentRow = React.memo(({
  assistant,
  onNavigate,
  onTestCall,
  onClone,
  onDelete,
  openDropdown,
  setOpenDropdown,
  formatDate
}: {
  assistant: Assistant;
  onNavigate: (id: string) => void;
  onTestCall: (id: string) => void;
  onClone: (id: string, name: string) => void;
  onDelete: (id: string) => void;
  openDropdown: string | null;
  setOpenDropdown: (id: string | null) => void;
  formatDate: (date: string) => string;
}) => {
  const handleRowClick = useCallback(() => {
    onNavigate(assistant.id);
  }, [assistant.id, onNavigate]);

  const handleDropdownToggle = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenDropdown(openDropdown === assistant.id ? null : assistant.id);
  }, [assistant.id, openDropdown, setOpenDropdown]);

  const handleTestCall = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onTestCall(assistant.id);
    setOpenDropdown(null);
  }, [assistant.id, onTestCall, setOpenDropdown]);

  const handleClone = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onClone(assistant.id, assistant.name);
    setOpenDropdown(null);
  }, [assistant.id, assistant.name, onClone, setOpenDropdown]);

  const handleDelete = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(assistant.id);
    setOpenDropdown(null);
  }, [assistant.id, onDelete, setOpenDropdown]);

  return (
    <div
      className="grid grid-cols-[2fr,1.5fr,1.5fr,100px] px-6 py-4 hover:bg-[#252525] transition-colors cursor-pointer border-b border-gray-800/50 last:border-b-0"
      onClick={handleRowClick}
    >
      <div className="text-sm font-medium truncate pr-4" title={assistant.name}>
        {assistant.name}
      </div>
      <div className="text-sm text-gray-400 truncate pr-4" title={assistant.model?.model || 'Not configured'}>
        {assistant.model?.model || 'Not configured'}
      </div>
      <div className="text-sm text-gray-400 pr-4">
        {formatDate(assistant.createdAt)}
      </div>
      <div className="flex justify-center">
        <div className="relative">
          <button
            className="p-2 hover:bg-[#333333] rounded-md transition-colors"
            onClick={handleDropdownToggle}
            title="More actions"
            aria-label={`Actions for ${assistant.name}`}
            aria-expanded={openDropdown === assistant.id}
          >
            <MoreHorizontal size={16} className="text-gray-400" />
          </button>

          {openDropdown === assistant.id && (
            <div className="absolute right-0 top-full mt-1 w-48 bg-[#1A1A1A] border border-gray-700 rounded-lg shadow-xl z-[9999] overflow-hidden">
              <div className="py-1">
                <button
                  className="w-full px-4 py-3 text-left text-sm hover:bg-[#252525] transition-colors flex items-center gap-3"
                  onClick={handleTestCall}
                >
                  <Phone size={16} className="text-green-400" />
                  Test call
                </button>
                <button
                  className="w-full px-4 py-3 text-left text-sm hover:bg-[#252525] transition-colors flex items-center gap-3"
                  onClick={handleClone}
                >
                  <Copy size={16} className="text-gray-400" />
                  Clone agent
                </button>
                <div className="border-t border-gray-700 my-1"></div>
                <button
                  className="w-full px-4 py-3 text-left text-sm hover:bg-[#252525] transition-colors flex items-center gap-3 text-red-400"
                  onClick={handleDelete}
                >
                  <Trash2 size={16} className="text-red-400" />
                  Delete agent
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

export const Agents: React.FC = () => {
  const navigate = useNavigate();
  const [isNewAgentModalOpen, setIsNewAgentModalOpen] = React.useState(false);
  const [agentName, setAgentName] = React.useState('');
  const [selectedTemplate, setSelectedTemplate] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = React.useState<string | null>(null);
  const [hasApiKey, setHasApiKey] = React.useState(false);
  const [openDropdown, setOpenDropdown] = React.useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = React.useState(false);

  // Debounced search for better performance
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Check for API key on mount
  React.useEffect(() => {
    const apiKey = import.meta.env.VITE_VAPI_API_KEY || localStorage.getItem('vapi_api_key');
    setHasApiKey(!!apiKey);
  }, []);

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = () => {
      setOpenDropdown(null);
    };

    if (openDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [openDropdown]);

  const {
    assistants,
    templates,
    loading,
    error,
    createAssistant,
    createAssistantFromTemplate,
    deleteAssistant,
    cloneAssistant,
    clearError,
    fetchAssistants,
  } = useAssistants();

  // Memoized handlers for better performance
  const handleNavigate = useCallback((id: string) => {
    navigate(`/app/agents/${id}`);
  }, [navigate]);

  const handleTestCall = useCallback((id: string) => {
    navigate(`/app/talk-to/${id}`);
  }, [navigate]);

  const handleClone = useCallback(async (id: string, originalName: string) => {
    try {
      await cloneAssistant(id, `${originalName} (Copy)`);
    } catch (err) {
      console.error('Failed to clone agent:', err);
    }
  }, [cloneAssistant]);

  const handleDeleteConfirm = useCallback((id: string) => {
    setShowDeleteConfirm(id);
  }, []);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await fetchAssistants();
    } finally {
      setIsRefreshing(false);
    }
  }, [fetchAssistants]);

  // Memoized filtered assistants for performance
  const filteredAssistants = useMemo(() => {
    if (!debouncedSearchQuery.trim()) return assistants;

    const query = debouncedSearchQuery.toLowerCase();
    return assistants.filter(assistant =>
      assistant.name.toLowerCase().includes(query) ||
      assistant.model?.model?.toLowerCase().includes(query)
    );
  }, [assistants, debouncedSearchQuery]);

  // Memoized date formatter
  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  }, []);

  const handleCreateAgent = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!agentName.trim()) return;

    try {
      let newAssistant: Assistant;

      if (selectedTemplate) {
        // Create from template
        newAssistant = await createAssistantFromTemplate(selectedTemplate, {
          name: agentName,
        });
      } else {
        // Create blank assistant
        newAssistant = await createAssistant({
          name: agentName,
          firstMessage: "Hello! How can I help you today?",
          model: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.7,
            maxTokens: 500,
            messages: [
              {
                role: 'system',
                content: 'You are a helpful AI assistant. Be polite, professional, and helpful.',
              },
            ],
          },
          voice: {
            provider: 'vapi',
            voiceId: 'Lily',
          },
        });
      }

      setIsNewAgentModalOpen(false);
      setAgentName('');
      setSelectedTemplate(null);

      // Navigate to the new agent's config page
      navigate(`/app/agents/${newAssistant.id}`);
    } catch (err) {
      console.error('Failed to create agent:', err);
    }
  };

  const handleDeleteAgent = useCallback(async (id: string) => {
    try {
      await deleteAssistant(id);
      setShowDeleteConfirm(null);
    } catch (err) {
      console.error('Failed to delete agent:', err);
    }
  }, [deleteAssistant]);

  // Show API key setup if no key is found
  if (!hasApiKey) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0F0F0F] text-white p-6">
        <ApiKeySetup onApiKeySet={() => setHasApiKey(true)} />
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-[#0F0F0F] text-white min-h-0">
      <div className="flex-1 overflow-y-auto p-6">
        <div className="max-w-[1200px] mx-auto h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <div className="flex items-center gap-3 mb-1">
              <h1 className="text-2xl font-semibold">Agents</h1>
              {assistants.length > 0 && (
                <span className="px-2 py-1 bg-[#1A1A1A] text-gray-400 text-xs rounded-full">
                  {filteredAssistants.length} of {assistants.length}
                </span>
              )}
            </div>
            <p className="text-gray-400">Create and manage your AI agents</p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing || loading}
              className="px-3 py-1.5 bg-[#1A1A1A] text-white rounded-lg text-sm hover:bg-[#252525] transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Refresh agents"
            >
              <RefreshCw size={14} className={isRefreshing ? 'animate-spin' : ''} />
              Refresh
            </button>
            <button className="px-3 py-1.5 bg-[#1A1A1A] text-white rounded-lg text-sm hover:bg-[#252525] transition-colors">
              Playground
            </button>
            <button
              onClick={() => setIsNewAgentModalOpen(true)}
              disabled={loading}
              className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Plus size={16} />
              New agent
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-900/20 border border-red-800 rounded-lg flex items-center gap-2">
            <AlertCircle size={18} className="text-red-400" />
            <span className="text-red-400">{error}</span>
            <button
              onClick={clearError}
              className="ml-auto text-red-400 hover:text-red-300"
            >
              ×
            </button>
          </div>
        )}

        {/* Search */}
        <div className="relative mb-6">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search agents by name or model..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
            autoComplete="off"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
              title="Clear search"
            >
              ×
            </button>
          )}
        </div>

        {/* Table */}
        <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 overflow-hidden flex-1 flex flex-col">
          <div className="grid grid-cols-[2fr,1.5fr,1.5fr,100px] px-6 py-4 border-b border-gray-800 bg-[#151515]">
            <div className="text-sm font-medium text-gray-300">Name</div>
            <div className="text-sm font-medium text-gray-300">Model</div>
            <div className="text-sm font-medium text-gray-300">Created</div>
            <div className="text-sm font-medium text-gray-300 text-center">Actions</div>
          </div>

          <div className="flex-1 flex flex-col">
          {loading && assistants.length === 0 ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-400">
                <div className="animate-spin w-8 h-8 border-2 border-gray-600 border-t-white rounded-full mx-auto mb-4"></div>
                <p className="text-lg font-medium mb-2">Loading agents...</p>
                <p className="text-sm">This may take a few moments</p>
              </div>
            </div>
          ) : filteredAssistants.length === 0 ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-400">
                {searchQuery ? (
                  <>
                    <Search size={48} className="mx-auto mb-4 text-gray-600" />
                    <p className="text-lg font-medium mb-2">No agents found</p>
                    <p className="text-sm">Try adjusting your search terms or create a new agent</p>
                  </>
                ) : (
                  <>
                    <Bot size={48} className="mx-auto mb-4 text-gray-600" />
                    <p className="text-lg font-medium mb-2">No agents created yet</p>
                    <p className="text-sm mb-4">Get started by creating your first AI agent</p>
                    <button
                      onClick={() => setIsNewAgentModalOpen(true)}
                      className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors"
                    >
                      Create your first agent
                    </button>
                  </>
                )}
              </div>
            </div>
          ) : (
            filteredAssistants.map((assistant) => (
              <AgentRow
                key={assistant.id}
                assistant={assistant}
                onNavigate={handleNavigate}
                onTestCall={handleTestCall}
                onClone={handleClone}
                onDelete={handleDeleteConfirm}
                openDropdown={openDropdown}
                setOpenDropdown={setOpenDropdown}
                formatDate={formatDate}
              />
            ))
          )}
          </div>
        </div>
        </div>
      </div>

      {/* New Agent Modal */}
      <Modal
        isOpen={isNewAgentModalOpen}
        onClose={() => setIsNewAgentModalOpen(false)}
        title="Create an AI agent"
      >
        <form onSubmit={handleCreateAgent}>
          <div className="mb-4">
            <label htmlFor="agentName" className="block text-sm font-medium text-gray-400 mb-1">
              AI Agent name
            </label>
            <input
              type="text"
              id="agentName"
              value={agentName}
              onChange={(e) => setAgentName(e.target.value)}
              placeholder="Customer support agent"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* Blank template */}
            <div
              className={`p-6 bg-[#0F0F0F] border rounded-lg cursor-pointer transition-colors ${
                selectedTemplate === null
                  ? 'border-white bg-[#1A1A1A]'
                  : 'border-gray-800 hover:border-gray-700'
              }`}
              onClick={() => setSelectedTemplate(null)}
            >
              <div className="flex items-center gap-2 mb-2">
                <Bot size={18} className="text-gray-400" />
                <span className="font-medium">Blank template</span>
              </div>
              <p className="text-sm text-gray-400 mb-4">Start with a blank template and customize your agent to suit your needs.</p>
              <div className="mt-3 flex items-center gap-1">
                <div className="w-6 h-6 rounded-full bg-purple-600 flex items-center justify-center text-xs">A</div>
                <span className="text-sm text-gray-400">Any</span>
              </div>
            </div>

            {/* Dynamic templates from API */}
            {templates.slice(0, 3).map((template) => {
              const getTemplateIcon = (category: string) => {
                switch (category) {
                  case 'support': return <Headphones size={18} className="text-gray-400" />;
                  case 'sales': return <CircleDollarSign size={18} className="text-gray-400" />;
                  case 'scheduling': return <Users size={18} className="text-gray-400" />;
                  default: return <Bot size={18} className="text-gray-400" />;
                }
              };

              const getTemplateColor = (category: string) => {
                switch (category) {
                  case 'support': return 'bg-blue-600';
                  case 'sales': return 'bg-red-600';
                  case 'scheduling': return 'bg-green-600';
                  default: return 'bg-purple-600';
                }
              };

              return (
                <div
                  key={template.id}
                  className={`p-6 bg-[#0F0F0F] border rounded-lg cursor-pointer transition-colors ${
                    selectedTemplate === template.id
                      ? 'border-white bg-[#1A1A1A]'
                      : 'border-gray-800 hover:border-gray-700'
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <div className="flex items-center gap-2 mb-2">
                    {getTemplateIcon(template.category)}
                    <span className="font-medium">{template.name}</span>
                  </div>
                  <p className="text-sm text-gray-400 mb-4">{template.description}</p>
                  <div className="mt-3 flex items-center gap-1">
                    <div className={`w-6 h-6 rounded-full ${getTemplateColor(template.category)} flex items-center justify-center text-xs`}>
                      {template.name.charAt(0)}
                    </div>
                    <span className="text-sm text-gray-400">{template.category}</span>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <button
              type="button"
              onClick={() => {
                setIsNewAgentModalOpen(false);
                setAgentName('');
                setSelectedTemplate(null);
              }}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Creating...' : 'Create agent'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(null)}
        title="Delete Agent"
      >
        <div className="space-y-4">
          <p className="text-gray-300">
            Are you sure you want to delete this agent? This action cannot be undone.
          </p>
          <div className="flex justify-end gap-3">
            <button
              onClick={() => setShowDeleteConfirm(null)}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => showDeleteConfirm && handleDeleteAgent(showDeleteConfirm)}
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};