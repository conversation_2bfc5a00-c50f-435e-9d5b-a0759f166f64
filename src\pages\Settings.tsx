import React from 'react';
import { Info } from 'lucide-react';

export const Settings: React.FC = () => {
  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-semibold mb-1">Conversational AI Settings</h1>
          <p className="text-gray-400">Configure workspace-wide settings for Conversational AI</p>
        </div>

        {/* Webhooks Section */}
        <div className="space-y-6">
          {/* Conversation Initiation Webhook */}
          <section className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h2 className="text-lg font-medium mb-1">Conversation Initiation Client Data Webhook</h2>
                <p className="text-sm text-gray-400">
                  Configure the webhook that will be called when a new Twilio phone call or SIP trunk call conversation begins.
                </p>
              </div>
              <button className="px-4 py-2 bg-[#252525] text-white rounded-lg text-sm hover:bg-[#333333] transition-colors">
                Add webhook
              </button>
            </div>
            <div className="flex items-center gap-2 p-3 bg-[#0F0F0F] rounded-lg border border-gray-800">
              <Info size={18} className="text-gray-400" />
              <p className="text-sm text-gray-400">No webhook configured</p>
            </div>
          </section>

          {/* Workspace Secrets */}
          <section className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h2 className="text-lg font-medium mb-1">Workspace Secrets</h2>
                <p className="text-sm text-gray-400">
                  Create and manage secure secrets that can be accessed across your workspace.
                </p>
              </div>
              <button className="px-4 py-2 bg-[#252525] text-white rounded-lg text-sm hover:bg-[#333333] transition-colors">
                Add secret
              </button>
            </div>
            <div className="flex items-center gap-2 p-3 bg-[#0F0F0F] rounded-lg border border-gray-800">
              <Info size={18} className="text-gray-400" />
              <p className="text-sm text-gray-400">No secrets added</p>
            </div>
          </section>

          {/* Post-Call Webhook */}
          <section className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h2 className="text-lg font-medium mb-1">Post-Call Webhook</h2>
                <p className="text-sm text-gray-400">
                  Select the webhook that will be called when a conversation ends. Webhooks can be managed in the settings page.
                </p>
              </div>
              <button className="px-4 py-2 bg-[#252525] text-white rounded-lg text-sm hover:bg-[#333333] transition-colors">
                Create Webhook
              </button>
            </div>
            <div className="flex items-center gap-2 p-3 bg-[#0F0F0F] rounded-lg border border-gray-800">
              <Info size={18} className="text-gray-400" />
              <p className="text-sm text-gray-400">No webhook configured</p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};