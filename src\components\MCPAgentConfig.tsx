import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Key, Brain, CheckCircle, AlertCircle, ExternalLink } from 'lucide-react';
import { Modal } from './Modal';

interface MCPAgentConfigProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MCPAgentConfig: React.FC<MCPAgentConfigProps> = ({ isOpen, onClose }) => {
  const [openRouterApiKey, setOpenRouterApiKey] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  const [isConfigured, setIsConfigured] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Popular OpenRouter models
  const popularModels = [
    { id: 'anthropic/claude-3.5-sonnet', name: 'Claude 3.5 Sonnet', provider: 'Anthropic' },
    { id: 'openai/gpt-4o', name: 'GPT-4o', provider: 'OpenA<PERSON>' },
    { id: 'openai/gpt-4o-mini', name: 'GPT-4o Mini', provider: 'OpenAI' },
    { id: 'anthropic/claude-3-haiku', name: '<PERSON> 3 <PERSON>ku', provider: 'Anthropic' },
    { id: 'meta-llama/llama-3.1-70b-instruct', name: 'Llama 3.1 70B', provider: 'Meta' },
    { id: 'google/gemini-pro-1.5', name: 'Gemini Pro 1.5', provider: 'Google' },
    { id: 'mistralai/mistral-large', name: 'Mistral Large', provider: 'Mistral AI' },
    { id: 'cohere/command-r-plus', name: 'Command R+', provider: 'Cohere' },
  ];

  useEffect(() => {
    // Load saved configuration
    const savedApiKey = localStorage.getItem('openrouter_api_key');
    const savedModel = localStorage.getItem('openrouter_model');
    
    if (savedApiKey) {
      setOpenRouterApiKey(savedApiKey);
      setIsConfigured(true);
    }
    
    if (savedModel) {
      setSelectedModel(savedModel);
    } else {
      setSelectedModel('anthropic/claude-3.5-sonnet'); // Default to Claude 3.5 Sonnet
    }
  }, []);

  const handleSave = async () => {
    if (!openRouterApiKey.trim()) {
      alert('Please enter your OpenRouter API key');
      return;
    }

    setIsSaving(true);
    
    try {
      // Save to localStorage (in production, you might want to use a more secure method)
      localStorage.setItem('openrouter_api_key', openRouterApiKey);
      localStorage.setItem('openrouter_model', selectedModel);
      
      // Set environment variables for the session
      (window as any).process = { env: { 
        VITE_OPENROUTER_API_KEY: openRouterApiKey,
        VITE_OPENROUTER_MODEL: selectedModel
      }};
      
      setIsConfigured(true);
      
      // Simulate save delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Configuration saved successfully! The MCP agent will use your OpenRouter settings.');
      onClose();
    } catch (error) {
      console.error('Error saving configuration:', error);
      alert('Error saving configuration. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConnection = async () => {
    if (!openRouterApiKey.trim()) {
      alert('Please enter your OpenRouter API key first');
      return;
    }

    try {
      // Test the API key by making a simple request
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${openRouterApiKey}`,
        },
      });

      if (response.ok) {
        alert('✅ Connection successful! Your API key is valid.');
      } else {
        alert('❌ Connection failed. Please check your API key.');
      }
    } catch (error) {
      alert('❌ Connection failed. Please check your internet connection and API key.');
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="MCP Agent Configuration">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
          <Brain className="text-blue-400" size={24} />
          <div>
            <h3 className="text-lg font-semibold text-white">OpenRouter Integration</h3>
            <p className="text-sm text-gray-400">Configure your MCP agent to use OpenRouter models</p>
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center gap-2">
          {isConfigured ? (
            <>
              <CheckCircle className="text-green-400" size={20} />
              <span className="text-green-400 text-sm">Agent configured and ready</span>
            </>
          ) : (
            <>
              <AlertCircle className="text-yellow-400" size={20} />
              <span className="text-yellow-400 text-sm">Agent needs configuration</span>
            </>
          )}
        </div>

        {/* API Key */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Key size={16} className="inline mr-2" />
            OpenRouter API Key
          </label>
          <input
            type="password"
            value={openRouterApiKey}
            onChange={(e) => setOpenRouterApiKey(e.target.value)}
            placeholder="sk-or-v1-..."
            className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm text-white placeholder-gray-500 focus:outline-none focus:border-gray-700 transition-colors"
          />
          <div className="flex items-center gap-2 mt-2">
            <button
              onClick={handleTestConnection}
              className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
            >
              Test Connection
            </button>
            <span className="text-gray-600">•</span>
            <a
              href="https://openrouter.ai/keys"
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-400 hover:text-blue-300 transition-colors flex items-center gap-1"
            >
              Get API Key <ExternalLink size={12} />
            </a>
          </div>
        </div>

        {/* Model Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Brain size={16} className="inline mr-2" />
            Model Selection
          </label>
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm text-white focus:outline-none focus:border-gray-700 transition-colors"
          >
            {popularModels.map((model) => (
              <option key={model.id} value={model.id}>
                {model.name} ({model.provider})
              </option>
            ))}
          </select>
          <p className="text-xs text-gray-500 mt-1">
            Choose the model that best fits your needs and budget
          </p>
        </div>

        {/* Info */}
        <div className="p-4 bg-gray-800/50 rounded-lg">
          <h4 className="text-sm font-medium text-white mb-2">Why OpenRouter?</h4>
          <ul className="text-xs text-gray-400 space-y-1">
            <li>• Access to multiple AI models from one API</li>
            <li>• Competitive pricing and pay-per-use billing</li>
            <li>• No vendor lock-in - switch models anytime</li>
            <li>• High availability and reliability</li>
          </ul>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving || !openRouterApiKey.trim()}
            className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Saving...
              </>
            ) : (
              <>
                <Settings size={16} />
                Save Configuration
              </>
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};
