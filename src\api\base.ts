import { VapiClient, VapiError } from '@vapi-ai/server-sdk';
import VapiConfig from './config.js';
import { ApiResponse, ErrorResponse } from './types.js';

/**
 * Base API service class with common functionality
 */
export abstract class BaseApiService {
  protected client: VapiClient;

  constructor(client?: VapiClient) {
    this.client = client || VapiConfig.getClient();
  }

  /**
   * Wrap API responses in a standard format
   */
  protected createResponse<T>(data: T, message?: string): ApiResponse<T> {
    return {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create error response
   */
  protected createErrorResponse(
    error: string | Error | VapiError,
    statusCode?: number
  ): ErrorResponse {
    let errorMessage: string;
    let details: any;
    let code = statusCode;

    if (error instanceof VapiError) {
      errorMessage = error.message;
      code = error.statusCode;
      details = error.body;
    } else if (error instanceof Error) {
      errorMessage = error.message;
    } else {
      errorMessage = error;
    }

    return {
      success: false,
      error: errorMessage,
      statusCode: code,
      timestamp: new Date().toISOString(),
      details,
    };
  }

  /**
   * Handle API calls with error wrapping
   */
  protected async handleApiCall<T>(
    apiCall: () => Promise<T>,
    successMessage?: string
  ): Promise<ApiResponse<T> | ErrorResponse> {
    try {
      const result = await apiCall();
      return this.createResponse(result, successMessage);
    } catch (error) {
      console.error('API call failed:', error);
      return this.createErrorResponse(error as Error);
    }
  }

  /**
   * Handle paginated API calls
   */
  protected async handlePaginatedApiCall<T>(
    apiCall: () => Promise<T[]>,
    page: number = 1,
    limit: number = 10,
    successMessage?: string
  ): Promise<ApiResponse<T[]> | ErrorResponse> {
    try {
      const result = await apiCall();

      // Simple pagination logic (you may need to adjust based on Vapi's actual pagination)
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedData = Array.isArray(result) ? result.slice(startIndex, endIndex) : result;

      return {
        success: true,
        data: paginatedData,
        message: successMessage,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Paginated API call failed:', error);
      return this.createErrorResponse(error as Error);
    }
  }

  /**
   * Validate required parameters
   */
  protected validateRequired(params: Record<string, any>, requiredFields: string[]): void {
    const missing = requiredFields.filter(field =>
      params[field] === undefined || params[field] === null || params[field] === ''
    );

    if (missing.length > 0) {
      throw new Error(`Missing required parameters: ${missing.join(', ')}`);
    }
  }

  /**
   * Sanitize and validate ID parameter
   */
  protected validateId(id: string, fieldName: string = 'id'): string {
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new Error(`Invalid ${fieldName}: must be a non-empty string`);
    }
    return id.trim();
  }

  /**
   * Create pagination metadata
   */
  protected createPaginationMeta(
    total: number,
    page: number,
    limit: number
  ) {
    const totalPages = Math.ceil(total / limit);

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Log API operations for debugging
   */
  protected logOperation(operation: string, params?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Vapi API] ${operation}`, params ? JSON.stringify(params, null, 2) : '');
    }
  }
}
