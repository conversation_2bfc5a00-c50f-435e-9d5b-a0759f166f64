import { Router, Request, Response } from 'express';
import { vapiServices } from '../../api/services/index.js';

const router = Router();

/**
 * GET /api/health
 * Basic health check endpoint
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const healthCheck = await vapiServices.healthCheck();

    const statusCode = healthCheck.status === 'healthy' ? 200 : 503;

    res.status(statusCode).json({
      ...healthCheck,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0',
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      error: 'Health check failed',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  }
});

/**
 * GET /api/health/detailed
 * Detailed health check with service-specific information
 */
router.get('/detailed', async (req: Request, res: Response) => {
  try {
    const healthCheck = await vapiServices.healthCheck();

    // Additional system information
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      environment: process.env.NODE_ENV || 'development',
    };

    // Check environment variables
    const envCheck = {
      vapiApiToken: !!process.env.VAPI_API_TOKEN,
      port: !!process.env.PORT,
      nodeEnv: !!process.env.NODE_ENV,
    };

    const statusCode = healthCheck.status === 'healthy' ? 200 : 503;

    res.status(statusCode).json({
      ...healthCheck,
      system: systemInfo,
      environment: envCheck,
      version: '1.0.0',
    });
  } catch (error) {
    console.error('Detailed health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      error: 'Detailed health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/health/services/:service
 * Check specific service health
 */
router.get('/services/:service', async (req: Request, res: Response) => {
  try {
    const { service } = req.params;

    let result;
    let statusCode = 200;

    switch (service) {
      case 'calls':
        try {
          await vapiServices.calls.listCalls({ limit: 1 });
          result = { status: 'healthy', service: 'calls' };
        } catch (error) {
          result = { status: 'unhealthy', service: 'calls', error: (error as Error).message };
          statusCode = 503;
        }
        break;

      case 'assistants':
        try {
          await vapiServices.assistants.listAssistants({ limit: 1 });
          result = { status: 'healthy', service: 'assistants' };
        } catch (error) {
          result = { status: 'unhealthy', service: 'assistants', error: (error as Error).message };
          statusCode = 503;
        }
        break;

      case 'phone-numbers':
        try {
          await vapiServices.phoneNumbers.listPhoneNumbers({ limit: 1 });
          result = { status: 'healthy', service: 'phone-numbers' };
        } catch (error) {
          result = { status: 'unhealthy', service: 'phone-numbers', error: (error as Error).message };
          statusCode = 503;
        }
        break;

      case 'templates':
        try {
          await vapiServices.templates.listTemplates();
          result = { status: 'healthy', service: 'templates' };
        } catch (error) {
          result = { status: 'unhealthy', service: 'templates', error: (error as Error).message };
          statusCode = 503;
        }
        break;

      case 'analytics':
        try {
          await vapiServices.analytics.getDashboardData();
          result = { status: 'healthy', service: 'analytics' };
        } catch (error) {
          result = { status: 'unhealthy', service: 'analytics', error: (error as Error).message };
          statusCode = 503;
        }
        break;

      default:
        return res.status(404).json({
          success: false,
          error: 'Service not found',
          availableServices: ['calls', 'assistants', 'phone-numbers', 'templates', 'analytics'],
          timestamp: new Date().toISOString(),
        });
    }

    res.status(statusCode).json({
      ...result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Service health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      service: req.params.service,
      error: 'Service health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/health/readiness
 * Kubernetes-style readiness probe
 */
router.get('/readiness', async (req: Request, res: Response) => {
  try {
    // Check if all critical services are ready
    const healthCheck = await vapiServices.healthCheck();

    // For readiness, we might be more strict about what services need to be healthy
    const criticalServices = ['calls', 'assistants'];
    const criticalServicesHealthy = criticalServices.every(
      service => healthCheck.services[service] === true
    );

    if (criticalServicesHealthy) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        status: 'not-ready',
        reason: 'Critical services are not healthy',
        services: healthCheck.services,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error('Readiness check failed:', error);
    res.status(503).json({
      status: 'not-ready',
      error: 'Readiness check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/health/liveness
 * Kubernetes-style liveness probe
 */
router.get('/liveness', (req: Request, res: Response) => {
  // Simple liveness check - if the server can respond, it's alive
  res.status(200).json({
    status: 'alive',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
  });
});

export default router;
