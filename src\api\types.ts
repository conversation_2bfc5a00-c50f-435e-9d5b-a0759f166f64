import { Vapi } from '@vapi-ai/server-sdk';

/**
 * Standard API Response wrapper
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

/**
 * Paginated response wrapper
 */
export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Error response structure
 */
export interface ErrorResponse {
  success: false;
  error: string;
  message?: string;
  statusCode?: number;
  timestamp: string;
  details?: any;
}

/**
 * Call-related types
 */
export interface CreateCallRequest {
  assistantId?: string;
  assistant?: Vapi.CreateAssistantDto;
  phoneNumberId?: string;
  customer?: {
    number?: string;
    name?: string;
    email?: string;
  };
  metadata?: Record<string, any>;
}

export interface CallListQuery {
  page?: number;
  limit?: number;
  assistantId?: string;
  phoneNumberId?: string;
  status?: string;
  createdAtGte?: string;
  createdAtLte?: string;
}

/**
 * Assistant-related types
 */
export interface AssistantListQuery {
  page?: number;
  limit?: number;
  name?: string;
  createdAtGte?: string;
  createdAtLte?: string;
}

export interface CreateAssistantRequest extends Vapi.CreateAssistantDto {
  // Add any custom fields if needed
}

export interface UpdateAssistantRequest extends Vapi.UpdateAssistantDto {
  // Add any custom fields if needed
}

/**
 * Phone Number-related types
 */
export interface PhoneNumberListQuery {
  page?: number;
  limit?: number;
  provider?: string;
  assignedToAssistant?: boolean;
}

export interface CreatePhoneNumberRequest {
  provider: string;
  credentialId: string;
  name?: string;
  assistantId?: string;
}

/**
 * Tool-related types
 */
export interface ToolListQuery {
  page?: number;
  limit?: number;
  type?: string;
}

export interface CreateToolRequest {
  type: string;
  name?: string;
  description?: string;
  // Additional tool-specific properties
  [key: string]: any;
}

/**
 * Analytics query types
 */
export interface AnalyticsQuery {
  table: string;
  name: string;
  operations: Array<{
    operation: string;
    column: string;
  }>;
  filters?: Array<{
    column: string;
    operator: string;
    value: any;
  }>;
  groupBy?: string[];
  orderBy?: Array<{
    column: string;
    direction: 'asc' | 'desc';
  }>;
}

export interface AnalyticsRequest {
  queries: AnalyticsQuery[];
  dateRange?: {
    start: string;
    end: string;
  };
}

/**
 * Webhook event types
 */
export interface WebhookEvent {
  type: string;
  data: any;
  timestamp: string;
  callId?: string;
  assistantId?: string;
}

/**
 * Scheduling types
 */
export interface ScheduleCallRequest {
  scheduledFor: string; // ISO date string
  assistantId?: string;
  phoneNumberId?: string;
  customer: {
    number: string;
    name?: string;
    email?: string;
  };
  metadata?: Record<string, any>;
}

/**
 * Template types for assistant configurations
 */
export interface AssistantTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  config: Vapi.CreateAssistantDto;
  tags: string[];
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: string;
  config: Vapi.CreateAssistantDto;
  tags?: string[];
  isPublic?: boolean;
}

/**
 * Workflow types
 */
export interface WorkflowListQuery {
  page?: number;
  limit?: number;
  name?: string;
}

/**
 * Squad types
 */
export interface SquadListQuery {
  page?: number;
  limit?: number;
  name?: string;
}

/**
 * Knowledge Base types
 */
export interface KnowledgeBaseListQuery {
  page?: number;
  limit?: number;
  provider?: string;
}

/**
 * Test Suite types
 */
export interface TestSuiteListQuery {
  page?: number;
  limit?: number;
  name?: string;
}

// Re-export Vapi types for convenience
export { Vapi };
