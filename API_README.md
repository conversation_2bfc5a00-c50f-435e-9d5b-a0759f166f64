# Vapi API Wrapper

A comprehensive TypeScript API wrapper for Vapi AI voice assistant platform, providing a production-ready backend service with all the endpoints you specified.

## 🚀 Features

- **Complete Vapi SDK Integration** - Full TypeScript support with the official Vapi server SDK
- **RESTful API Design** - Clean, consistent API endpoints following REST principles
- **Production Ready** - Error handling, logging, health checks, and monitoring
- **Template System** - Pre-built assistant configurations for common use cases
- **Analytics & Insights** - Comprehensive analytics and reporting capabilities
- **Webhook Support** - Handle real-time events from Vapi
- **Type Safety** - Full TypeScript support with proper type definitions

## 📋 API Endpoints

### 🤖 Call Management
- `POST /api/calls/start` - Start an outbound call
- `GET /api/calls` - List all calls with filtering
- `GET /api/calls/:id` - Get call details/status
- `POST /api/calls/:id/end` - End a live call early
- `GET /api/calls/:id/transcript` - Fetch full transcript
- `GET /api/calls/:id/audio` - Stream or download recording

### 🧠 Assistant Management
- `GET /api/assistants` - List available assistants
- `POST /api/assistants` - Create new assistant
- `GET /api/assistants/:id` - Get full assistant config
- `PATCH /api/assistants/:id` - Update assistant config
- `DELETE /api/assistants/:id` - Delete assistant

### 📱 Phone Number Management
- `GET /api/phone-numbers` - List all purchased/assigned numbers
- `POST /api/phone-numbers` - Purchase/create new phone number
- `GET /api/phone-numbers/:id` - Get phone number config
- `PATCH /api/phone-numbers/:id` - Assign assistant or webhook
- `DELETE /api/phone-numbers/:id` - Delete/release phone number

### 📋 Templates
- `GET /api/templates` - List available assistant templates
- `POST /api/templates` - Create new template
- `GET /api/templates/:id` - Get specific template
- `PATCH /api/templates/:id` - Update template
- `DELETE /api/templates/:id` - Delete template
- `POST /api/templates/:id/create-assistant` - Create assistant from template

### 📊 Analytics & Insights
- `POST /api/analytics/query` - Execute custom analytics queries
- `GET /api/analytics/calls` - Get call statistics
- `GET /api/analytics/leads` - Get lead attribution data
- `GET /api/analytics/agents/:id` - Get agent-specific metrics
- `GET /api/analytics/dashboard` - Get real-time dashboard data

### 🔄 Webhooks
- `POST /api/webhooks/vapi` - Handle Vapi event stream
- `POST /api/webhooks/completions` - Handle LLM completions
- `POST /api/webhooks/call-events` - Handle call events

### 🏥 Health & Monitoring
- `GET /api/health` - Basic health check
- `GET /api/health/detailed` - Detailed system health
- `GET /api/health/services/:service` - Check specific service
- `GET /api/health/readiness` - Kubernetes readiness probe
- `GET /api/health/liveness` - Kubernetes liveness probe

## 🛠 Installation & Setup

### 1. Install Dependencies

```bash
npm install @vapi-ai/server-sdk express cors helmet morgan dotenv
npm install --save-dev @types/express @types/cors @types/morgan tsx
```

### 2. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Vapi API Configuration
VAPI_API_TOKEN=your_vapi_api_token_here
VAPI_BASE_URL=https://api.vapi.ai

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
```

### 3. Start the Development Server

```bash
npm run server:dev
```

The API server will start on `http://localhost:3001`

### 4. Production Build

```bash
npm run server:build
npm run server:start
```

## 📚 Usage Examples

### Creating a Call

```typescript
POST /api/calls/start
Content-Type: application/json

{
  "assistantId": "assistant_123",
  "customer": {
    "number": "+1234567890",
    "name": "John Doe"
  },
  "metadata": {
    "campaignId": "campaign_456"
  }
}
```

### Creating an Assistant from Template

```typescript
POST /api/templates/customer-support/create-assistant
Content-Type: application/json

{
  "name": "My Customer Support Bot",
  "firstMessage": "Hello! How can I help you today?"
}
```

### Getting Analytics

```typescript
GET /api/analytics/calls?start=2024-01-01&end=2024-01-31&assistantId=assistant_123
```

## 🏗 Architecture

### Service Layer Structure

```
src/api/
├── config.ts          # Vapi client configuration
├── types.ts           # TypeScript type definitions
├── base.ts            # Base service class
└── services/
    ├── calls.ts       # Call management
    ├── assistants.ts  # Assistant management
    ├── phoneNumbers.ts # Phone number management
    ├── templates.ts   # Template management
    ├── analytics.ts   # Analytics & insights
    └── index.ts       # Service factory
```

### Route Layer Structure

```
src/server/
├── app.ts             # Express app configuration
├── index.ts           # Server entry point
└── routes/
    ├── calls.ts       # Call endpoints
    ├── assistants.ts  # Assistant endpoints
    ├── phoneNumbers.ts # Phone number endpoints
    ├── templates.ts   # Template endpoints
    ├── analytics.ts   # Analytics endpoints
    ├── webhooks.ts    # Webhook handlers
    └── health.ts      # Health check endpoints
```

## 🔧 Configuration

### Vapi Client Configuration

The Vapi client is configured in `src/api/config.ts` with:
- Automatic token management
- Configurable timeouts and retries
- Environment-based configuration
- Singleton pattern for efficiency

### Error Handling

All API responses follow a consistent format:

```typescript
// Success Response
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}

// Error Response
{
  "success": false,
  "error": "Error message",
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 🎯 Built-in Templates

The system includes pre-configured assistant templates:

1. **Customer Support Agent** - For handling customer inquiries
2. **Sales Representative** - For lead qualification and conversion
3. **Appointment Scheduler** - For booking and managing appointments
4. **Lead Qualification Agent** - For BANT qualification
5. **Survey & Feedback Collector** - For collecting customer feedback

## 🔍 Monitoring & Health Checks

### Health Check Endpoints

- `/api/health` - Basic health status
- `/api/health/detailed` - Comprehensive system information
- `/api/health/services/:service` - Individual service health
- `/api/health/readiness` - Kubernetes readiness probe
- `/api/health/liveness` - Kubernetes liveness probe

### Logging

The system includes comprehensive logging:
- Request/response logging with Morgan
- Service operation logging
- Error tracking and reporting
- Development vs production log levels

## 🚀 Deployment

### Docker Support

Create a `Dockerfile`:

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3001
CMD ["npm", "run", "server:start"]
```

### Environment Variables for Production

```env
NODE_ENV=production
VAPI_API_TOKEN=your_production_token
PORT=3001
CORS_ORIGIN=https://your-frontend-domain.com
```

## 🤝 Contributing

1. Follow the existing code structure and patterns
2. Add proper TypeScript types for all new features
3. Include error handling and logging
4. Update this README for any new endpoints or features
5. Test all endpoints before submitting

## 📄 License

This project is licensed under the MIT License.
