import { BaseApiService } from '../base.js';
import {
  ApiResponse,
  ErrorResponse,
  AssistantTemplate,
  CreateTemplateRequest,
  Vapi
} from '../types.js';

/**
 * Assistant Templates Service
 * Handles template management for base assistant configurations
 */
export class TemplatesService extends BaseApiService {

  // In-memory storage for templates (in production, use a database)
  private templates: Map<string, AssistantTemplate> = new Map();

  constructor() {
    super();
    this.initializeDefaultTemplates();
  }

  /**
   * Initialize default assistant templates
   */
  private initializeDefaultTemplates(): void {
    const defaultTemplates: AssistantTemplate[] = [
      {
        id: 'customer-support',
        name: 'Customer Support Agent',
        description: 'A helpful customer support assistant for handling inquiries and issues',
        category: 'support',
        config: {
          name: 'Customer Support Assistant',
          model: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.7,
            maxTokens: 500,
          },
          voice: {
            provider: 'elevenlabs',
            voiceId: 'default',
          },
          firstMessage: 'Hello! I\'m here to help you with any questions or issues you might have. How can I assist you today?',
          systemMessage: 'You are a helpful customer support representative. Be polite, professional, and try to resolve customer issues efficiently.',
        },
        tags: ['support', 'customer-service', 'help'],
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'sales-agent',
        name: 'Sales Representative',
        description: 'A persuasive sales assistant for lead qualification and conversion',
        category: 'sales',
        config: {
          name: 'Sales Assistant',
          model: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.8,
            maxTokens: 600,
          },
          voice: {
            provider: 'elevenlabs',
            voiceId: 'professional',
          },
          firstMessage: 'Hi there! I\'d love to learn more about your needs and see how we can help your business grow. What brings you here today?',
          systemMessage: 'You are a skilled sales representative. Focus on understanding customer needs, building rapport, and guiding them toward a solution.',
        },
        tags: ['sales', 'lead-generation', 'conversion'],
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'appointment-scheduler',
        name: 'Appointment Scheduler',
        description: 'An efficient assistant for scheduling and managing appointments',
        category: 'scheduling',
        config: {
          name: 'Scheduling Assistant',
          model: {
            provider: 'openai',
            model: 'gpt-3.5-turbo',
            temperature: 0.5,
            maxTokens: 400,
          },
          voice: {
            provider: 'elevenlabs',
            voiceId: 'friendly',
          },
          firstMessage: 'Hello! I can help you schedule an appointment. What type of service are you looking for and when would work best for you?',
          systemMessage: 'You are an appointment scheduling assistant. Be efficient, ask for necessary details, and confirm all appointment information clearly.',
        },
        tags: ['scheduling', 'appointments', 'calendar'],
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'lead-qualifier',
        name: 'Lead Qualification Agent',
        description: 'A specialized assistant for qualifying and scoring potential leads',
        category: 'sales',
        config: {
          name: 'Lead Qualifier',
          model: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.6,
            maxTokens: 500,
          },
          voice: {
            provider: 'elevenlabs',
            voiceId: 'professional',
          },
          firstMessage: 'Hi! I\'d like to learn a bit about your business to see if we might be a good fit. Could you tell me about your current challenges?',
          systemMessage: 'You are a lead qualification specialist. Ask targeted questions to understand budget, authority, need, and timeline (BANT). Be conversational but focused.',
        },
        tags: ['sales', 'qualification', 'leads', 'bant'],
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'survey-collector',
        name: 'Survey & Feedback Collector',
        description: 'An assistant designed to collect customer feedback and conduct surveys',
        category: 'research',
        config: {
          name: 'Survey Assistant',
          model: {
            provider: 'openai',
            model: 'gpt-3.5-turbo',
            temperature: 0.4,
            maxTokens: 300,
          },
          voice: {
            provider: 'elevenlabs',
            voiceId: 'neutral',
          },
          firstMessage: 'Hello! I\'d love to get your feedback to help us improve our services. This will only take a few minutes. Shall we begin?',
          systemMessage: 'You are conducting a survey. Ask questions clearly, be patient with responses, and ensure you collect all required information.',
        },
        tags: ['survey', 'feedback', 'research', 'data-collection'],
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * List all available templates
   */
  async listTemplates(
    category?: string,
    isPublic?: boolean
  ): Promise<ApiResponse<AssistantTemplate[]> | ErrorResponse> {
    this.logOperation('listTemplates', { category, isPublic });

    return this.handleApiCall(async () => {
      let templates = Array.from(this.templates.values());

      // Filter by category if provided
      if (category) {
        templates = templates.filter(template => template.category === category);
      }

      // Filter by public status if provided
      if (isPublic !== undefined) {
        templates = templates.filter(template => template.isPublic === isPublic);
      }

      return templates.sort((a, b) => a.name.localeCompare(b.name));
    }, 'Templates retrieved successfully');
  }

  /**
   * Get a specific template by ID
   */
  async getTemplate(id: string): Promise<ApiResponse<AssistantTemplate> | ErrorResponse> {
    this.logOperation('getTemplate', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'template ID');
      const template = this.templates.get(validId);

      if (!template) {
        throw new Error(`Template with ID '${validId}' not found`);
      }

      return template;
    }, 'Template retrieved successfully');
  }

  /**
   * Create a new template
   */
  async createTemplate(request: CreateTemplateRequest): Promise<ApiResponse<AssistantTemplate> | ErrorResponse> {
    this.logOperation('createTemplate', request);

    return this.handleApiCall(async () => {
      // Validate required fields
      this.validateRequired(request, ['name', 'description', 'category', 'config']);

      const templateId = `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      const template: AssistantTemplate = {
        id: templateId,
        name: request.name,
        description: request.description,
        category: request.category,
        config: request.config,
        tags: request.tags || [],
        isPublic: request.isPublic || false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      this.templates.set(templateId, template);

      return template;
    }, 'Template created successfully');
  }

  /**
   * Update an existing template
   */
  async updateTemplate(
    id: string,
    updates: Partial<CreateTemplateRequest>
  ): Promise<ApiResponse<AssistantTemplate> | ErrorResponse> {
    this.logOperation('updateTemplate', { id, updates });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'template ID');
      const existingTemplate = this.templates.get(validId);

      if (!existingTemplate) {
        throw new Error(`Template with ID '${validId}' not found`);
      }

      const updatedTemplate: AssistantTemplate = {
        ...existingTemplate,
        ...updates,
        id: validId, // Ensure ID doesn't change
        updatedAt: new Date().toISOString(),
      };

      this.templates.set(validId, updatedTemplate);

      return updatedTemplate;
    }, 'Template updated successfully');
  }

  /**
   * Delete a template
   */
  async deleteTemplate(id: string): Promise<ApiResponse<{ deleted: boolean }> | ErrorResponse> {
    this.logOperation('deleteTemplate', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'template ID');
      const template = this.templates.get(validId);

      if (!template) {
        throw new Error(`Template with ID '${validId}' not found`);
      }

      // Don't allow deletion of default public templates
      if (template.isPublic && !validId.startsWith('custom-')) {
        throw new Error('Cannot delete default public templates');
      }

      const deleted = this.templates.delete(validId);

      return { deleted };
    }, 'Template deleted successfully');
  }

  /**
   * Create an assistant from a template
   */
  async createAssistantFromTemplate(
    templateId: string,
    customizations?: Partial<Vapi.CreateAssistantDto>
  ): Promise<ApiResponse<Vapi.Assistant> | ErrorResponse> {
    this.logOperation('createAssistantFromTemplate', { templateId, customizations });

    return this.handleApiCall(async () => {
      const validTemplateId = this.validateId(templateId, 'template ID');
      const template = this.templates.get(validTemplateId);

      if (!template) {
        throw new Error(`Template with ID '${validTemplateId}' not found`);
      }

      // Merge template config with customizations
      const assistantConfig: Vapi.CreateAssistantDto = {
        ...template.config,
        ...customizations,
      };

      // Create the assistant using the Vapi client
      return await this.client.assistants.create(assistantConfig);
    }, 'Assistant created from template successfully');
  }

  /**
   * Get template categories
   */
  async getTemplateCategories(): Promise<ApiResponse<string[]> | ErrorResponse> {
    this.logOperation('getTemplateCategories');

    return this.handleApiCall(async () => {
      const categories = Array.from(
        new Set(Array.from(this.templates.values()).map(template => template.category))
      ).sort();

      return categories;
    }, 'Template categories retrieved successfully');
  }
}
