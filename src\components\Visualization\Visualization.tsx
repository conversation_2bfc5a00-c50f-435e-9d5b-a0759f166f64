import React from 'react';
import { Volume2, Mic } from 'lucide-react';

interface VisualizationProps {
  isListening?: boolean;
}

export const Visualization: React.FC<VisualizationProps> = ({ isListening = false }) => {
  const bars = 24; // Increased number of bars

  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      {/* Background gradient circles */}
      <div className={`absolute inset-0 rounded-full bg-gradient-to-br from-purple-600 to-cyan-400 opacity-20 transition-all duration-700 animate-pulse-slow ${isListening ? 'scale-100 blur-md' : 'scale-95'}`} />
      <div className={`absolute inset-12 rounded-full bg-gradient-to-tr from-purple-500 to-cyan-500 opacity-30 transition-all duration-700 animate-rotate-slow ${isListening ? 'scale-100 blur-sm' : 'scale-90'}`} />
      <div className={`absolute inset-24 rounded-full bg-gradient-to-r from-purple-700 to-cyan-600 opacity-40 transition-all duration-700 ${isListening ? 'scale-100' : 'scale-85'}`} />
      <div className="absolute inset-24 rounded-full bg-[#0F0F0F] shadow-2xl overflow-hidden">
        <div className={`absolute inset-0 bg-gradient-to-br from-purple-500/10 to-cyan-400/10 transition-opacity duration-700 ${isListening ? 'opacity-100' : 'opacity-0'}`} />
        <div className={`absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.1)_0%,transparent_70%)] animate-pulse-slow`} />
      </div>

      {/* Audio visualization */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className={`relative w-48 h-48 transition-all duration-300 ${isListening ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
          {[...Array(bars)].map((_, i) => (
            <div
              key={i}
              className="absolute w-full h-full"
              style={{
                transform: `rotate(${i * (360 / bars)}deg)`,
              }}
            >
              <div
                className={`w-1 h-12 bg-gradient-to-b from-purple-500 via-cyan-400 to-blue-500 rounded-full transform origin-bottom ${
                  isListening ? 'animate-sound-bar' : ''
                }`}
                style={{
                  animationDelay: `${i * (1.2 / bars)}s`,
                }}
              />
            </div>
          ))}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className={`p-8 rounded-full bg-black/30 backdrop-blur-sm transition-all duration-300 ${
              isListening ? 'scale-110 shadow-[0_0_30px_rgba(168,85,247,0.4)]' : 'scale-100'
            }`}>
              <div className="relative">
                {/* Pulsing background */}
                <div className={`absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/20 to-cyan-400/20 blur-sm transition-opacity duration-300 ${
                  isListening ? 'opacity-100' : 'opacity-0'
                }`} />
                
                {/* Icon container */}
                <div className="relative z-10 bg-black/50 p-4 rounded-full">
                {isListening ? (
                  <Volume2 size={28} className="text-white animate-pulse" />
                ) : (
                  <Mic size={28} className="text-gray-400" />
                )}
                </div>
                
                {/* Decorative rings */}
                <div className={`absolute -inset-2 rounded-full border border-purple-500/20 transition-all duration-300 ${
                  isListening ? 'scale-110 opacity-100' : 'scale-90 opacity-0'
                }`} />
                <div className={`absolute -inset-4 rounded-full border border-cyan-400/20 transition-all duration-300 ${
                  isListening ? 'scale-110 opacity-100 rotate-45' : 'scale-90 opacity-0'
                }`} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Info text */}
      <div className="absolute -bottom-16 text-center text-gray-400 text-sm space-y-1">
        <p>In-development calls are 50% off</p>
        <a href="#" className="text-white hover:underline transition-colors">Learn more</a>
      </div>
    </div>
  );
};