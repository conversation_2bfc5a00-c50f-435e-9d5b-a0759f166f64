import React from 'react';
import { Volume2, Mic } from 'lucide-react';
import { OrbVisualization } from './OrbVisualizationNew';

interface VisualizationProps {
  isListening?: boolean;
  isSpeaking?: boolean;
  isCallActive?: boolean;
}

export const Visualization: React.FC<VisualizationProps> = ({
  isListening = false,
  isSpeaking = false,
  isCallActive = false
}) => {
  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      {/* 3D Orb Visualization */}
      <OrbVisualization isListening={isListening} isSpeaking={isSpeaking} isCallActive={isCallActive} />


    </div>
  );
};