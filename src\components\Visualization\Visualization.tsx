import React from 'react';
import { Volume2, Mic } from 'lucide-react';
import { OrbVisualization } from './OrbVisualization';

interface VisualizationProps {
  isListening?: boolean;
  isSpeaking?: boolean;
}

export const Visualization: React.FC<VisualizationProps> = ({
  isListening = false,
  isSpeaking = false
}) => {
  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      {/* 3D Orb Visualization */}
      <OrbVisualization isListening={isListening} isSpeaking={isSpeaking} />


    </div>
  );
};