import React from 'react';
import { OrbVisualization } from './OrbVisualization';

interface VisualizationProps {
  isListening?: boolean;
  isSpeaking?: boolean;
  isCallActive?: boolean;
}

export const Visualization: React.FC<VisualizationProps> = ({
  isListening = false,
  isSpeaking = false,
  isCallActive = false
}) => {

  return (
    <OrbVisualization
      isListening={isListening}
      isSpeaking={isSpeaking}
      isCallActive={isCallActive}
    />
  );
};