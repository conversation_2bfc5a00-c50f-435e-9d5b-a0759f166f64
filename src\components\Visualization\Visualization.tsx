import React, { useEffect, useState } from 'react';
import { Volume2, Mic, Loader2 } from 'lucide-react';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';

interface VisualizationProps {
  isListening?: boolean;
}

export const Visualization: React.FC<VisualizationProps> = ({ isListening: propIsListening }) => {
  // Get the listening state, speaking state, and connection status from Redux if not provided as prop
  const { isListening: storeIsListening, isSpeaking: storeIsSpeaking, status } = useSelector((state: RootState) => state.conversations);

  // Use prop if provided, otherwise use store value
  const [isListening, setIsListening] = useState(propIsListening || false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);

  useEffect(() => {
    if (propIsListening !== undefined) {
      setIsListening(propIsListening);
    } else {
      setIsListening(storeIsListening);
    }

    // Update connecting state based on status
    setIsConnecting(status === 'connecting');

    // Update speaking state from Redux
    setIsSpeaking(storeIsSpeaking);
  }, [propIsListening, storeIsListening, storeIsSpeaking, status]);

  const bars = 24; // Increased number of bars

  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      {/* Background gradient circles */}
      <div className={`absolute inset-0 rounded-full bg-gradient-to-br from-purple-600 to-cyan-400 opacity-20 transition-all duration-700 animate-pulse-slow ${(isListening || isSpeaking) ? 'scale-100 blur-md' : isConnecting ? 'scale-100 blur-sm' : 'scale-95'}`} />
      <div className={`absolute inset-12 rounded-full bg-gradient-to-tr from-purple-500 to-cyan-500 opacity-30 transition-all duration-700 animate-rotate-slow ${(isListening || isSpeaking) ? 'scale-100 blur-sm' : isConnecting ? 'scale-95' : 'scale-90'}`} />
      <div className={`absolute inset-24 rounded-full bg-gradient-to-r from-purple-700 to-cyan-600 opacity-40 transition-all duration-700 ${(isListening || isSpeaking) ? 'scale-100' : isConnecting ? 'scale-90' : 'scale-85'}`} />
      <div className="absolute inset-24 rounded-full bg-[#0F0F0F] shadow-2xl overflow-hidden">
        <div className={`absolute inset-0 bg-gradient-to-br from-purple-500/10 to-cyan-400/10 transition-opacity duration-700 ${(isListening || isSpeaking) ? 'opacity-100' : isConnecting ? 'opacity-50' : 'opacity-0'}`} />
        <div className={`absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.1)_0%,transparent_70%)] animate-pulse-slow`} />
      </div>

      {/* Audio visualization */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className={`relative w-48 h-48 transition-all duration-300 ${(isListening || isSpeaking) ? 'opacity-100 scale-100' : isConnecting ? 'opacity-70 scale-95' : 'opacity-0 scale-95'}`}>
          {[...Array(bars)].map((_, i) => (
            <div
              key={i}
              className="absolute w-full h-full"
              style={{
                transform: `rotate(${i * (360 / bars)}deg)`,
              }}
            >
              <div
                className={`w-1 h-12 bg-gradient-to-b from-purple-500 via-cyan-400 to-blue-500 rounded-full transform origin-bottom ${
                  isListening ? 'animate-sound-bar' :
                  isSpeaking ? 'animate-sound-bar-speaking' :
                  isConnecting ? 'animate-sound-bar-connecting' : ''
                }`}
                style={{
                  animationDelay: `${i * (1.2 / bars)}s`,
                }}
              />
            </div>
          ))}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className={`p-8 rounded-full bg-black/30 backdrop-blur-sm transition-all duration-300 ${
              isListening ? 'scale-110 shadow-[0_0_30px_rgba(168,85,247,0.4)]' : 'scale-100'
            }`}>
              <div className="relative">
                {/* Pulsing background */}
                <div className={`absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/20 to-cyan-400/20 blur-sm transition-opacity duration-300 ${
                  isListening ? 'opacity-100' : 'opacity-0'
                }`} />

                {/* Icon container */}
                <div className="relative z-10 bg-black/50 p-4 rounded-full">
                {isConnecting ? (
                  <Loader2 size={28} className="text-white animate-spin" />
                ) : isListening ? (
                  <Mic size={28} className="text-white animate-pulse" />
                ) : isSpeaking ? (
                  <Volume2 size={28} className="text-white animate-pulse" />
                ) : (
                  <Mic size={28} className="text-gray-400" />
                )}
                </div>

                {/* Decorative rings */}
                <div className={`absolute -inset-2 rounded-full border border-purple-500/20 transition-all duration-300 ${
                  isListening ? 'scale-110 opacity-100' :
                  isSpeaking ? 'scale-105 opacity-80' :
                  isConnecting ? 'scale-100 opacity-50' : 'scale-90 opacity-0'
                }`} />
                <div className={`absolute -inset-4 rounded-full border border-cyan-400/20 transition-all duration-300 ${
                  isListening ? 'scale-110 opacity-100 rotate-45' :
                  isSpeaking ? 'scale-105 opacity-80 rotate-30' :
                  isConnecting ? 'scale-100 opacity-50 rotate-15' : 'scale-90 opacity-0'
                }`} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};