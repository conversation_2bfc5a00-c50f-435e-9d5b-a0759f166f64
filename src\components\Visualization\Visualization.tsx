import React from 'react';
import { Volume2, Mic } from 'lucide-react';
import { OrbVisualization } from './OrbVisualization';

interface VisualizationProps {
  isListening?: boolean;
  isSpeaking?: boolean;
}

export const Visualization: React.FC<VisualizationProps> = ({
  isListening = false,
  isSpeaking = false
}) => {
  return (
    <div className="relative w-[400px] h-[400px] flex items-center justify-center">
      {/* 3D Orb Visualization */}
      <OrbVisualization isListening={isListening} isSpeaking={isSpeaking} />

      {/* Overlay controls */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className={`transition-all duration-300 ${
          isListening || isSpeaking ? 'opacity-80 scale-90' : 'opacity-100 scale-100'
        }`}>
          <div className={`p-8 rounded-full bg-black/20 backdrop-blur-sm transition-all duration-300 ${
            isListening || isSpeaking ? 'scale-110 shadow-[0_0_30px_rgba(168,85,247,0.4)]' : 'scale-100'
          }`}>
            <div className="relative">
              {/* Pulsing background */}
              <div className={`absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/20 to-cyan-400/20 blur-sm transition-opacity duration-300 ${
                isListening || isSpeaking ? 'opacity-100' : 'opacity-0'
              }`} />

              {/* Icon container */}
              <div className="relative z-10 bg-black/30 p-4 rounded-full">
                {isSpeaking ? (
                  <Volume2 size={28} className="text-purple-400 animate-pulse" />
                ) : isListening ? (
                  <Volume2 size={28} className="text-cyan-400 animate-pulse" />
                ) : (
                  <Mic size={28} className="text-gray-400" />
                )}
              </div>

              {/* Decorative rings */}
              <div className={`absolute -inset-2 rounded-full border border-purple-500/20 transition-all duration-300 ${
                isListening || isSpeaking ? 'scale-110 opacity-100' : 'scale-90 opacity-0'
              }`} />
              <div className={`absolute -inset-4 rounded-full border border-cyan-400/20 transition-all duration-300 ${
                isListening || isSpeaking ? 'scale-110 opacity-100 rotate-45' : 'scale-90 opacity-0'
              }`} />
            </div>
          </div>
        </div>
      </div>

      {/* Info text */}
      <div className="absolute -bottom-16 text-center text-gray-400 text-sm space-y-1">
        <p>In-development calls are 50% off</p>
        <a href="#" className="text-white hover:underline transition-colors">Learn more</a>
      </div>
    </div>
  );
};