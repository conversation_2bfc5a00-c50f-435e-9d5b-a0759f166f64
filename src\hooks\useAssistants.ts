import { useState, useEffect, useCallback } from 'react';
import { api<PERSON><PERSON>, Assistant, AssistantTemplate, CreateAssistantRequest } from '../services/api';

export const useAssistants = () => {
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [templates, setTemplates] = useState<AssistantTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAssistants = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getAssistants();
      if (response.success && response.data) {
        setAssistants(response.data);
      } else {
        setError(response.error || 'Failed to fetch assistants');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch assistants');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getTemplates();
      if (response.success && response.data) {
        setTemplates(response.data);
      } else {
        setError(response.error || 'Failed to fetch templates');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  }, []);

  const createAssistant = useCallback(async (assistant: CreateAssistantRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.createAssistant(assistant);
      if (response.success && response.data) {
        setAssistants(prev => [...prev, response.data!]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create assistant');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create assistant';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const createAssistantFromTemplate = useCallback(async (
    templateId: string, 
    customizations?: Partial<CreateAssistantRequest>
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.createAssistantFromTemplate(templateId, customizations);
      if (response.success && response.data) {
        setAssistants(prev => [...prev, response.data!]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create assistant from template');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create assistant from template';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const updateAssistant = useCallback(async (id: string, updates: Partial<CreateAssistantRequest>) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.updateAssistant(id, updates);
      if (response.success && response.data) {
        setAssistants(prev => 
          prev.map(assistant => 
            assistant.id === id ? response.data! : assistant
          )
        );
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to update assistant');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update assistant';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteAssistant = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.deleteAssistant(id);
      if (response.success) {
        setAssistants(prev => prev.filter(assistant => assistant.id !== id));
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete assistant');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete assistant';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const cloneAssistant = useCallback(async (id: string, newName?: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.cloneAssistant(id, newName);
      if (response.success && response.data) {
        setAssistants(prev => [...prev, response.data!]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to clone assistant');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clone assistant';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const getAssistant = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getAssistant(id);
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get assistant');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get assistant';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const testAssistant = useCallback(async (id: string, testMessage?: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.testAssistant(id, testMessage);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to test assistant');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to test assistant';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAssistants();
    fetchTemplates();
  }, [fetchAssistants, fetchTemplates]);

  return {
    assistants,
    templates,
    loading,
    error,
    fetchAssistants,
    fetchTemplates,
    createAssistant,
    createAssistantFromTemplate,
    updateAssistant,
    deleteAssistant,
    cloneAssistant,
    getAssistant,
    testAssistant,
    clearError: () => setError(null),
  };
};
