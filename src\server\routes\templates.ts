import { Router, Request, Response } from 'express';
import { vapiServices } from '../../api/services/index.js';
import { CreateTemplateRequest } from '../../api/types.js';

const router = Router();

/**
 * GET /api/templates
 * List all available templates
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const category = req.query.category as string;
    const isPublic = req.query.isPublic === 'true' ? true :
                    req.query.isPublic === 'false' ? false : undefined;

    const result = await vapiServices.templates.listTemplates(category, isPublic);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error listing templates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to list templates',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/templates/categories
 * Get all template categories
 */
router.get('/categories', async (req: Request, res: Response) => {
  try {
    const result = await vapiServices.templates.getTemplateCategories();

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting template categories:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get template categories',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/templates
 * Create a new template
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const templateRequest: CreateTemplateRequest = req.body;
    const result = await vapiServices.templates.createTemplate(templateRequest);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    console.error('Error creating template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create template',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * GET /api/templates/:id
 * Get a specific template
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.templates.getTemplate(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error getting template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get template',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * PATCH /api/templates/:id
 * Update an existing template
 */
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    const result = await vapiServices.templates.updateTemplate(id, updates);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error updating template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update template',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * DELETE /api/templates/:id
 * Delete a template
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await vapiServices.templates.deleteTemplate(id);

    if ('error' in result) {
      return res.status(result.statusCode || 404).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('Error deleting template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete template',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * POST /api/templates/:id/create-assistant
 * Create an assistant from a template
 */
router.post('/:id/create-assistant', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const customizations = req.body;

    const result = await vapiServices.templates.createAssistantFromTemplate(id, customizations);

    if ('error' in result) {
      return res.status(result.statusCode || 400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    console.error('Error creating assistant from template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create assistant from template',
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
