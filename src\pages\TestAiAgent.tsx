import React from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Visualization } from '../components/Visualization';
import { Mi<PERSON>, MicO<PERSON>, Settings, ChevronRight, AlertCircle } from 'lucide-react';

// TypeScript declarations for Web Speech API
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}

export const TestAiAgent: React.FC = () => {
  const { agentId } = useParams();
  const navigate = useNavigate();
  const [isListening, setIsListening] = React.useState(false);
  const [isSpeaking, setIsSpeaking] = React.useState(false);
  const [transcript, setTranscript] = React.useState('');
  const [error, setError] = React.useState<string | null>(null);

  const recognitionRef = React.useRef<SpeechRecognition | null>(null);
  const synthRef = React.useRef<SpeechSynthesis | null>(null);

  // Initialize speech recognition and synthesis
  React.useEffect(() => {
    // Check if browser supports speech recognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (!SpeechRecognition) {
      setError('Speech recognition not supported in this browser');
      return;
    }

    // Initialize speech recognition
    const recognition = new SpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      console.log('Speech recognition started');
      setError(null);
    };

    recognition.onresult = (event) => {
      let finalTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        }
      }

      if (finalTranscript) {
        console.log('Final transcript:', finalTranscript);
        setTranscript(finalTranscript);
        handleUserSpeech(finalTranscript);
      }
    };

    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      setError(`Speech recognition error: ${event.error}`);
      setIsListening(false);
    };

    recognition.onend = () => {
      console.log('Speech recognition ended');
      setIsListening(false);
    };

    recognitionRef.current = recognition;

    // Initialize speech synthesis
    if ('speechSynthesis' in window) {
      synthRef.current = window.speechSynthesis;
    } else {
      setError('Speech synthesis not supported in this browser');
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (synthRef.current) {
        synthRef.current.cancel();
      }
    };
  }, []);

  const handleUserSpeech = async (userMessage: string) => {
    try {
      // Try to get a real AI response from your agent
      const aiResponse = await getAIResponse(userMessage);
      speakResponse(aiResponse);
    } catch (err) {
      console.error('Error processing speech:', err);
      setError('Error processing your message');
      // Fallback to simulation if real AI fails
      const fallbackResponse = await simulateAIResponse(userMessage);
      speakResponse(fallbackResponse);
    }
  };

  const getAIResponse = async (userMessage: string): Promise<string> => {
    if (!agentId) {
      throw new Error('No agent ID provided');
    }

    try {
      // Call your Vapi assistant API
      const response = await fetch(`/api/assistants/${agentId}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          testMessage: userMessage,
        }),
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
      }

      const data = await response.json();

      // Extract the response text from the API response
      // Adjust this based on your actual API response structure
      return data.response || data.testResult || "I received your message but couldn't generate a response.";
    } catch (error) {
      console.error('Error calling AI agent:', error);
      throw error;
    }
  };

  const simulateAIResponse = async (userMessage: string): Promise<string> => {
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simple response logic as fallback
    const responses = [
      "I understand you said: " + userMessage + ". How can I help you further?",
      "That's interesting. Can you tell me more about that?",
      "I heard you mention: " + userMessage + ". What would you like to know?",
      "Thank you for sharing that. Is there anything specific I can assist you with?",
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  const speakResponse = (text: string) => {
    if (!synthRef.current) {
      setError('Speech synthesis not available');
      return;
    }

    // Cancel any ongoing speech
    synthRef.current.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1;
    utterance.volume = 0.8;

    utterance.onstart = () => {
      console.log('AI started speaking');
      setIsSpeaking(true);
    };

    utterance.onend = () => {
      console.log('AI finished speaking');
      setIsSpeaking(false);
    };

    utterance.onerror = (event) => {
      console.error('Speech synthesis error:', event.error);
      setError(`Speech synthesis error: ${event.error}`);
      setIsSpeaking(false);
    };

    synthRef.current.speak(utterance);
  };

  const toggleListening = () => {
    if (!recognitionRef.current) {
      setError('Speech recognition not initialized');
      return;
    }

    if (isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    } else {
      try {
        recognitionRef.current.start();
        setIsListening(true);
        setTranscript('');
        setError(null);
      } catch (err) {
        console.error('Error starting recognition:', err);
        setError('Error starting speech recognition');
      }
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#0F0F0F] text-white pt-6">
      {/* Main content */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <Visualization isListening={isListening} isSpeaking={isSpeaking} />

        {/* Controls */}
        <div className="mt-12 flex flex-col items-center gap-4">
          <button
            onClick={toggleListening}
            disabled={isSpeaking}
            className={`w-16 h-16 rounded-full flex items-center justify-center transition-all ${
              isSpeaking
                ? 'bg-gray-600 cursor-not-allowed'
                : isListening
                ? 'bg-red-500 hover:bg-red-600 scale-110'
                : 'bg-white hover:bg-gray-100'
            }`}
          >
            {isListening ? (
              <MicOff size={24} className="text-white" />
            ) : (
              <Mic size={24} className={isSpeaking ? "text-gray-400" : "text-black"} />
            )}
          </button>

          <p className="text-sm text-gray-400 text-center">
            {isSpeaking
              ? 'Agent is speaking...'
              : isListening
              ? 'Listening... Tap to stop'
              : 'Tap to talk'}
          </p>

          {/* Error display */}
          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm bg-red-900/20 px-4 py-2 rounded-lg">
              <AlertCircle size={16} />
              <span>{error}</span>
            </div>
          )}

          {/* Transcript display */}
          {transcript && (
            <div className="max-w-md text-center">
              <p className="text-xs text-gray-500 mb-1">You said:</p>
              <p className="text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg">
                "{transcript}"
              </p>
            </div>
          )}

          {/* Instructions */}
          <div className="text-xs text-gray-500 text-center max-w-sm mt-4">
            <p>Make sure to allow microphone access when prompted.</p>
            <p>Speak clearly and the agent will respond to you.</p>
          </div>
        </div>
      </div>
    </div>
  );
};