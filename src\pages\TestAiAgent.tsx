import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Visualization } from '../components/Visualization';
import { Mic, MicOff, Settings, ChevronRight, AlertCircle, Phone, PhoneOff } from 'lucide-react';
import Vapi from '@vapi-ai/web';

export const TestAiAgent: React.FC = () => {
  const { agentId } = useParams();
  const navigate = useNavigate();
  const [isCallActive, setIsCallActive] = React.useState(false);
  const [isListening, setIsListening] = React.useState(false);
  const [isSpeaking, setIsSpeaking] = React.useState(false);
  const [transcript, setTranscript] = React.useState('');
  const [error, setError] = React.useState<string | null>(null);
  const [callStatus, setCallStatus] = React.useState<string>('Ready to call');

  const vapiRef = React.useRef<Vapi | null>(null);

  // Initialize Vapi
  React.useEffect(() => {
    try {
      const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;

      // Validate the public key
      if (!publicKey || publicKey === 'pk_your_actual_public_key_here' || publicKey === 'your_vapi_public_key_here') {
        setError('Please set your VITE_VAPI_PUBLIC_KEY in the .env file');
        return;
      }

      // Initialize Vapi with your public key
      const vapi = new Vapi(publicKey);
      vapiRef.current = vapi;

      // Set up event listeners
      vapi.on('call-start', () => {
        console.log('Call started');
        setIsCallActive(true);
        setCallStatus('Call active');
        setError(null);
      });

      vapi.on('call-end', () => {
        console.log('Call ended');
        setIsCallActive(false);
        setIsListening(false);
        setIsSpeaking(false);
        setCallStatus('Call ended');
      });

      vapi.on('speech-start', () => {
        console.log('Assistant started speaking');
        setIsSpeaking(true);
        setIsListening(false);
      });

      vapi.on('speech-end', () => {
        console.log('Assistant stopped speaking');
        setIsSpeaking(false);
      });

      vapi.on('message', (message) => {
        console.log('Received message:', message);

        // Handle transcript messages
        if (message.type === 'transcript') {
          if (message.transcriptType === 'final') {
            setTranscript(message.transcript);
            setIsListening(false);
          } else if (message.transcriptType === 'partial') {
            setIsListening(true);
          }
        }
      });

      vapi.on('error', (error) => {
        console.error('Vapi error:', error);
        setError(`Call error: ${error.message || 'Unknown error'}`);
        setIsCallActive(false);
        setIsListening(false);
        setIsSpeaking(false);
      });

      return () => {
        if (vapi) {
          vapi.stop();
        }
      };
    } catch (err) {
      console.error('Failed to initialize Vapi:', err);
      setError('Failed to initialize voice system. Please check your configuration.');
    }
  }, []);

  const startCall = async () => {
    if (!vapiRef.current || !agentId) {
      setError('Voice system not initialized or no agent selected');
      return;
    }

    try {
      setCallStatus('Connecting...');
      setError(null);

      // Start the call with the agent ID
      await vapiRef.current.start(agentId);
    } catch (err) {
      console.error('Error starting call:', err);
      setError('Failed to start call. Please try again.');
      setCallStatus('Ready to call');
    }
  };

  const endCall = () => {
    if (!vapiRef.current) {
      setError('Voice system not initialized');
      return;
    }

    try {
      vapiRef.current.stop();
      setCallStatus('Ending call...');
    } catch (err) {
      console.error('Error ending call:', err);
      setError('Failed to end call');
    }
  };

  const toggleCall = () => {
    if (isCallActive) {
      endCall();
    } else {
      startCall();
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#0F0F0F] text-white pt-6">
      {/* Main content */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <Visualization isListening={isListening} isSpeaking={isSpeaking} isCallActive={isCallActive} />

        {/* Controls */}
        <div className="mt-12 flex flex-col items-center gap-4">
          <button
            onClick={toggleCall}
            disabled={callStatus === 'Connecting...' || callStatus === 'Ending call...'}
            className={`w-16 h-16 rounded-full flex items-center justify-center transition-all ${
              callStatus === 'Connecting...' || callStatus === 'Ending call...'
                ? 'bg-gray-600 cursor-not-allowed'
                : isCallActive
                ? 'bg-red-500 hover:bg-red-600 scale-110'
                : 'bg-green-500 hover:bg-green-600'
            }`}
          >
            {callStatus === 'Connecting...' || callStatus === 'Ending call...' ? (
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
            ) : isCallActive ? (
              <PhoneOff size={24} className="text-white" />
            ) : (
              <Phone size={24} className="text-white" />
            )}
          </button>

          {/* Error display - only show errors */}
          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm bg-red-900/20 px-4 py-2 rounded-lg max-w-md">
              <AlertCircle size={16} />
              <span>{error}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};