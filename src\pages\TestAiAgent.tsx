import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Visualization } from '../components/Visualization';
import { Mic, MicOff, Settings, ChevronRight } from 'lucide-react';

export const TestAiAgent: React.FC = () => {
  const { agentId } = useParams();
  const navigate = useNavigate();
  const [isListening, setIsListening] = React.useState(false);

  const toggleListening = () => {
    setIsListening(!isListening);
  };

  return (
    <div className="flex flex-col h-full bg-[#0F0F0F] text-white pt-6">
      {/* Main content */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <Visualization isListening={isListening} />

        {/* Controls */}
        <div className="mt-12 flex flex-col items-center gap-4">
          <button
            onClick={toggleListening}
            className={`w-16 h-16 rounded-full flex items-center justify-center transition-all ${
              isListening 
                ? 'bg-red-500 hover:bg-red-600 scale-110' 
                : 'bg-white hover:bg-gray-100'
            }`}
          >
            {isListening ? (
              <MicOff size={24} className="text-white" />
            ) : (
              <Mic size={24} className="text-black" />
            )}
          </button>
          <p className="text-sm text-gray-400">
            {isListening ? 'Tap to stop' : 'Tap to talk'}
          </p>
        </div>
      </div>
    </div>
  );
};