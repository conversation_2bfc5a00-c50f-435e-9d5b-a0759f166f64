import { BaseApiService } from '../base.js';
import {
  <PERSON><PERSON><PERSON><PERSON>po<PERSON>,
  <PERSON>rrorR<PERSON>ponse,
  AssistantListQuery,
  CreateAssistantRequest,
  UpdateAssistantRequest,
  Vapi
} from '../types.js';

/**
 * Assistant Management Service
 * Handles all assistant-related operations including creating, retrieving, updating, and deleting assistants
 */
export class AssistantsService extends BaseApiService {

  /**
   * Create a new assistant
   */
  async createAssistant(request: CreateAssistantRequest): Promise<ApiResponse<Vapi.Assistant> | ErrorResponse> {
    this.logOperation('createAssistant', request);

    return this.handleApiCall(async () => {
      // Validate required fields
      this.validateRequired(request, ['name']);

      return await this.client.assistants.create(request);
    }, 'Assistant created successfully');
  }

  /**
   * List all assistants with optional filtering
   */
  async listAssistants(query: AssistantListQuery = {}): Promise<ApiResponse<Vapi.Assistant[]> | ErrorResponse> {
    this.logOperation('listAssistants', query);

    return this.handleApiCall(async () => {
      const requestParams: any = {};

      // Add query parameters if provided
      if (query.name) requestParams.name = query.name;
      if (query.createdAtGte) requestParams.createdAtGte = query.createdAtGte;
      if (query.createdAtLte) requestParams.createdAtLte = query.createdAtLte;

      return await this.client.assistants.list(requestParams);
    }, 'Assistants retrieved successfully');
  }

  /**
   * Get a specific assistant by ID
   */
  async getAssistant(id: string): Promise<ApiResponse<Vapi.Assistant> | ErrorResponse> {
    this.logOperation('getAssistant', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'assistant ID');
      return await this.client.assistants.get(validId);
    }, 'Assistant retrieved successfully');
  }

  /**
   * Update an existing assistant
   */
  async updateAssistant(
    id: string,
    updates: UpdateAssistantRequest
  ): Promise<ApiResponse<Vapi.Assistant> | ErrorResponse> {
    this.logOperation('updateAssistant', { id, updates });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'assistant ID');
      return await this.client.assistants.update(validId, updates);
    }, 'Assistant updated successfully');
  }

  /**
   * Delete an assistant
   */
  async deleteAssistant(id: string): Promise<ApiResponse<Vapi.Assistant> | ErrorResponse> {
    this.logOperation('deleteAssistant', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'assistant ID');
      return await this.client.assistants.delete(validId);
    }, 'Assistant deleted successfully');
  }

  /**
   * Clone an existing assistant
   */
  async cloneAssistant(
    id: string,
    newName?: string
  ): Promise<ApiResponse<Vapi.Assistant> | ErrorResponse> {
    this.logOperation('cloneAssistant', { id, newName });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'assistant ID');

      // Get the original assistant
      const originalAssistant = await this.client.assistants.get(validId);

      // Create a new assistant based on the original
      const cloneData: CreateAssistantRequest = {
        ...originalAssistant,
        name: newName || `${originalAssistant.name} (Copy)`,
        // Remove fields that shouldn't be copied
        id: undefined,
        createdAt: undefined,
        updatedAt: undefined,
      };

      return await this.client.assistants.create(cloneData);
    }, 'Assistant cloned successfully');
  }

  /**
   * Get assistant performance metrics
   */
  async getAssistantMetrics(id: string): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getAssistantMetrics', { id });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'assistant ID');

      // Get assistant details
      const assistant = await this.client.assistants.get(validId);

      // Get calls for this assistant (you might need to implement this based on available analytics)
      const calls = await this.client.calls.list({ assistantId: validId });

      // Calculate basic metrics
      const totalCalls = calls.length;
      const successfulCalls = calls.filter(call => call.status === 'completed').length;
      const failedCalls = calls.filter(call => call.status === 'failed').length;
      const averageDuration = calls.reduce((sum, call) => {
        if (call.endedAt && call.startedAt) {
          return sum + (new Date(call.endedAt).getTime() - new Date(call.startedAt).getTime());
        }
        return sum;
      }, 0) / totalCalls;

      return {
        assistantId: validId,
        assistantName: assistant.name,
        metrics: {
          totalCalls,
          successfulCalls,
          failedCalls,
          successRate: totalCalls > 0 ? (successfulCalls / totalCalls) * 100 : 0,
          averageDuration: averageDuration || 0,
        },
        lastUpdated: new Date().toISOString(),
      };
    }, 'Assistant metrics retrieved successfully');
  }

  /**
   * Test an assistant configuration
   */
  async testAssistant(
    id: string,
    testMessage?: string
  ): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('testAssistant', { id, testMessage });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'assistant ID');

      // This would typically create a test call or simulation
      // Implementation depends on Vapi's testing capabilities
      const assistant = await this.client.assistants.get(validId);

      return {
        assistantId: validId,
        testMessage: testMessage || 'Hello, this is a test message',
        assistantConfig: assistant,
        testResult: 'Test configuration validated successfully',
        timestamp: new Date().toISOString(),
      };
    }, 'Assistant test completed successfully');
  }

  /**
   * Get assistant usage statistics
   */
  async getAssistantUsage(
    id: string,
    dateRange?: { start: string; end: string }
  ): Promise<ApiResponse<any> | ErrorResponse> {
    this.logOperation('getAssistantUsage', { id, dateRange });

    return this.handleApiCall(async () => {
      const validId = this.validateId(id, 'assistant ID');

      // Build query parameters for date range
      const queryParams: any = { assistantId: validId };
      if (dateRange?.start) queryParams.createdAtGte = dateRange.start;
      if (dateRange?.end) queryParams.createdAtLte = dateRange.end;

      // Get calls for this assistant within the date range
      const calls = await this.client.calls.list(queryParams);

      // Calculate usage statistics
      const totalMinutes = calls.reduce((sum, call) => {
        if (call.endedAt && call.startedAt) {
          const duration = new Date(call.endedAt).getTime() - new Date(call.startedAt).getTime();
          return sum + (duration / (1000 * 60)); // Convert to minutes
        }
        return sum;
      }, 0);

      const totalCost = calls.reduce((sum, call) => sum + (call.cost || 0), 0);

      return {
        assistantId: validId,
        dateRange: dateRange || { start: 'all-time', end: 'now' },
        usage: {
          totalCalls: calls.length,
          totalMinutes: Math.round(totalMinutes * 100) / 100,
          totalCost: Math.round(totalCost * 100) / 100,
          averageCallDuration: calls.length > 0 ? totalMinutes / calls.length : 0,
        },
        lastUpdated: new Date().toISOString(),
      };
    }, 'Assistant usage statistics retrieved successfully');
  }
}
