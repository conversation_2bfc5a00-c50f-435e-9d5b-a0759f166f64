import express from 'express';
import cors from 'cors';
import { config } from '../api/config.js';

async function testRoute(routeName: string, routePath: string) {
  try {
    console.log(`Testing ${routeName}...`);
    const app = express();
    app.use(cors());
    app.use(express.json());
    
    const router = (await import(routePath)).default;
    app.use('/api/test', router);
    
    console.log(`✓ ${routeName} loaded successfully`);
    return true;
  } catch (error) {
    console.error(`✗ Error loading ${routeName}:`, error);
    return false;
  }
}

async function isolateIssue() {
  const routes = [
    { name: 'Health', path: './routes/health.js' },
    { name: 'Calls', path: './routes/calls.js' },
    { name: 'Assistants', path: './routes/assistants.js' },
    { name: 'Phone Numbers', path: './routes/phoneNumbers.js' },
    { name: 'Templates', path: './routes/templates.js' },
    { name: 'Analytics', path: './routes/analytics.js' },
    { name: 'Webhooks', path: './routes/webhooks.js' },
  ];

  for (const route of routes) {
    const success = await testRoute(route.name, route.path);
    if (!success) {
      console.log(`Issue found in ${route.name} route`);
      break;
    }
  }
}

isolateIssue();
