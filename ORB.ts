import { useRef, useEffect, useMemo, useCallback } from 'react';
import * as THREE from 'three';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

interface ColorShiftVisualizerProps {
isListening: boolean;
}

// Updated palette to match the reference image with blue-purple glow
const palette = [
new THREE.Color('#0B0B3B'), // Deep Blue (inner core)
new THREE.Color('#3A0CA3'), // Deep Purple
new THREE.Color('#480CA8'), // Rich Purple
new THREE.Color('#5B11DF'), // Bright Purple
new THREE.Color('#7209B7'), // Vibrant Purple
new THREE.Color('#4361EE'), // Bright Blue (outer edge)
new THREE.Color('#4CC9F0'), // Electric Blue (outer glow)
];

// Specific palettes for different audio states with blues and purples
const userSpeakingPalette = [
new THREE.Color('#00FFF7'), // Cyan/Aqua
new THREE.Color('#00CFFF'), // Electric Blue
new THREE.Color('#0080FF'), // Bright Blue
new THREE.Color('#3B82F6'), // Blue
new THREE.Color('#06B6D4'), // Cyan
];

// Updated palette to match the reference image with blue-purple glow
const agentSpeakingPalette = [
new THREE.Color('#0B0B3B'), // Deep Blue (inner core)
new THREE.Color('#3A0CA3'), // Deep Purple
new THREE.Color('#480CA8'), // Rich Purple
new THREE.Color('#5B11DF'), // Bright Purple
new THREE.Color('#7209B7'), // Vibrant Purple
new THREE.Color('#4361EE'), // Bright Blue (outer edge)
new THREE.Color('#4CC9F0'), // Electric Blue (outer glow)
];

const bothSpeakingPalette = [
new THREE.Color('#D355FF'), // Bright Purple
new THREE.Color('#C084FC'), // Light Purple
new THREE.Color('#A259FF'), // Purple
new THREE.Color('#8A2BE2'), // Violet
new THREE.Color('#9400D3'), // Dark Violet
];

export function ColorShiftVisualizer({ isListening }: ColorShiftVisualizerProps) {
const containerRef = useRef<HTMLDivElement>(null);
const sceneRef = useRef<THREE.Scene | null>(null);
const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
const particleSystemRef = useRef<THREE.Points | null>(null);
const connectionsRef = useRef<THREE.LineSegments | null>(null);
const solidSphereRef = useRef<THREE.Mesh | null>(null); // Reference for the solid sphere
const frameIdRef = useRef<number>(0);
const timeStartRef = useRef<number>(Date.now());

// Get speaking state from Redux to detect agent's voice
const isSpeaking = useSelector((state: RootState) => state.conversations.isSpeaking);

// Initialize Three.js scene
useEffect(() => {
if (!containerRef.current) return;


console.log('Initializing 3D scene');

// Create scene
const scene = new THREE.Scene();
sceneRef.current = scene;

// Create camera
const camera = new THREE.PerspectiveCamera(
  65, // Field of view
  containerRef.current.clientWidth / containerRef.current.clientHeight, // Aspect ratio
  0.1, // Near clipping plane
  1000 // Far clipping plane
);
// Position camera for a 3/4 view that better shows the sphere's dimensionality
camera.position.x = 180; // Position to the right
camera.position.y = 180; // Position above
camera.position.z = 180; // Position in front
camera.lookAt(0, 0, 0); // Look at the center of the sphere
cameraRef.current = camera;

// Create renderer
const renderer = new THREE.WebGLRenderer({
  antialias: true,
  alpha: true // Allow transparency
});
renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
renderer.setClearColor(0x000000, 0); // Transparent background
containerRef.current.appendChild(renderer.domElement);
rendererRef.current = renderer;

// Create particles geometry
const particles = new THREE.BufferGeometry();
const particleCount = 4000;

// Arrays to store particle positions, initial positions, and colors
const positions = new Float32Array(particleCount * 3);
const initialPositions = new Float32Array(particleCount * 3);
const colors = new Float32Array(particleCount * 3); // RGB colors for each particle
const latitudes = new Float32Array(particleCount); // Store latitude (phi) for color mapping
const longitudes = new Float32Array(particleCount); // Store longitude (theta) for color mapping

// Create a hollow sphere with evenly distributed particles
const radius = 120; // Reduced radius to make the sphere smaller

// Use the Fibonacci sphere algorithm for more uniform distribution
const goldenRatio = (1 + Math.sqrt(5)) / 2;
const angleIncrement = Math.PI * 2 * goldenRatio;

let idx = 0;
for (let i = 0; i < particleCount; i++) {
  // Calculate the spherical coordinates using Fibonacci distribution
  const t = i / particleCount;
  const inclination = Math.acos(1 - 2 * t);
  const azimuth = angleIncrement * i;

  // Convert to Cartesian coordinates
  const x = Math.sin(inclination) * Math.cos(azimuth) * radius;
  const y = Math.sin(inclination) * Math.sin(azimuth) * radius;
  const z = Math.cos(inclination) * radius;

  const j = idx * 3;

  // Set position
  positions[j] = x;
  positions[j + 1] = y;
  positions[j + 2] = z;

  // Store initial position for animation
  initialPositions[j] = x;
  initialPositions[j + 1] = y;
  initialPositions[j + 2] = z;

  // Store normalized latitude and longitude for color mapping
  latitudes[idx] = inclination / Math.PI; // 0 to 1 (north to south pole)
  longitudes[idx] = (azimuth / (2 * Math.PI)) % 1.0; // 0 to 1 (around the sphere)

  // Initialize with white color (will be updated in animation loop)
  colors[j] = 1.0;     // R
  colors[j + 1] = 1.0; // G
  colors[j + 2] = 1.0; // B

  idx++;
}

particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
particles.userData.initialPositions = initialPositions;
particles.userData.latitudes = latitudes;
particles.userData.longitudes = longitudes;

// Create particle material
let particleTexture;
try {
  particleTexture = new THREE.TextureLoader().load('/particle.png');
} catch (e) {
  // Create a fallback texture if loading fails
  const canvas = document.createElement('canvas');
  canvas.width = 64;
  canvas.height = 64;
  const context = canvas.getContext('2d');

  if (context) {
    const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
    gradient.addColorStop(0.3, 'rgba(240, 240, 255, 0.8)');
    gradient.addColorStop(0.7, 'rgba(210, 210, 255, 0.3)');
    gradient.addColorStop(1, 'rgba(200, 200, 255, 0)');

    context.fillStyle = gradient;
    context.beginPath();
    context.arc(32, 32, 32, 0, Math.PI * 2);
    context.fill();
  }

  particleTexture = new THREE.CanvasTexture(canvas);
}

const particleMaterial = new THREE.PointsMaterial({
  vertexColors: true, // Use colors from vertices
  size: 3.0, // Slightly larger particles for better visibility
  map: particleTexture,
  blending: THREE.AdditiveBlending,
  transparent: true,
  depthWrite: false
});

// Create particle system
const particleSystem = new THREE.Points(particles, particleMaterial);
scene.add(particleSystem);
particleSystemRef.current = particleSystem;

// Create connections between particles for mesh effect with consistent appearance
const connectionsMaterial = new THREE.LineBasicMaterial({
  color: 0xffffff,
  transparent: true,
  opacity: 0.3, // Consistent opacity
  blending: THREE.AdditiveBlending,
  vertexColors: true,
  linewidth: 1, // Note: linewidth > 1 not supported in WebGL, but we set it anyway
  depthTest: false, // Prevent depth testing which can cause inconsistent appearance
  depthWrite: false // Prevent writing to depth buffer
});

// Create connections geometry
const connectionsGeometry = new THREE.BufferGeometry();

// Create a more complete mesh across the entire sphere
const connectionIndices: number[] = [];

// Use Delaunay triangulation approach for more uniform mesh
// We'll connect each particle to its nearest neighbors based on angular distance
const maxConnectionsPerParticle = 6; // Each particle connects to up to 6 neighbors

// For each particle, find its nearest neighbors
for (let i = 0; i < particleCount; i++) {
  const j = i * 3;
  const x1 = positions[j];
  const y1 = positions[j + 1];
  const z1 = positions[j + 2];

  // Calculate normalized direction vector (for angular comparison)
  const length1 = Math.sqrt(x1 * x1 + y1 * y1 + z1 * z1);
  const nx1 = x1 / length1;
  const ny1 = y1 / length1;
  const nz1 = z1 / length1;

  // Store distances to all other particles
  interface NeighborInfo {
    index: number;
    dotProduct: number; // Higher dot product means closer angular distance
  }

  const neighbors: NeighborInfo[] = [];

  // Find angular distances to other particles
  for (let k = 0; k < particleCount; k++) {
    if (i === k) continue; // Skip self

    const m = k * 3;
    const x2 = positions[m];
    const y2 = positions[m + 1];
    const z2 = positions[m + 2];

    // Calculate normalized direction vector
    const length2 = Math.sqrt(x2 * x2 + y2 * y2 + z2 * z2);
    const nx2 = x2 / length2;
    const ny2 = y2 / length2;
    const nz2 = z2 / length2;

    // Calculate dot product (higher means closer in angle)
    const dotProduct = nx1 * nx2 + ny1 * ny2 + nz1 * nz2;

    // Only consider particles that are somewhat close (dot product > 0.7 means within ~45 degrees)
    if (dotProduct > 0.7) {
      neighbors.push({ index: k, dotProduct });
    }
  }

  // Sort by dot product (descending) to get closest neighbors first
  neighbors.sort((a, b) => b.dotProduct - a.dotProduct);

  // Connect to closest neighbors (up to maxConnectionsPerParticle)
  const connectCount = Math.min(maxConnectionsPerParticle, neighbors.length);
  for (let n = 0; n < connectCount; n++) {
    connectionIndices.push(i, neighbors[n].index);
  }
}

// Create index buffer for connections
connectionsGeometry.setIndex(connectionIndices);
connectionsGeometry.setAttribute('position', particles.getAttribute('position'));
connectionsGeometry.setAttribute('color', particles.getAttribute('color'));

// Create line segments
const connections = new THREE.LineSegments(connectionsGeometry, connectionsMaterial);
connections.renderOrder = 1; // Ensure it renders after (on top of) the solid sphere
scene.add(connections);
connectionsRef.current = connections;

// Make the connections barely larger than the solid sphere
connections.scale.set(1.005, 1.005, 1.005);

// Create a solid sphere with smooth shading - smaller than the particle sphere to fit inside the mesh
const sphereGeometry = new THREE.SphereGeometry(radius * 0.98, 64, 64); // 98% of the mesh size

// Create vertex shader with displacement for the solid sphere
const vertexShader = `
  uniform float time;
  uniform float bassIntensity;
  uniform float midIntensity;
  uniform float trebleIntensity;
  uniform float isSpeaking;
  uniform float isListening;

  varying vec2 vUv;
  varying vec3 vNormal;
  varying vec3 vPosition;
  varying vec3 vOrigPosition;

  // Function to create organic, cloud-like ripple effect similar to the reference image
  // with additional pole protection to prevent tearing
  float ripple(vec2 position, vec2 center, float time, float frequency, float speed, float amplitude) {
    float distance = length(position - center);

    // Create base ripple effect with sine wave
    float baseRipple = sin(distance * frequency - time * speed);

    // Add secondary ripple with different frequency for more complex patterns
    float secondaryRipple = 0.5 * sin(distance * frequency * 1.5 - time * speed * 0.8);

    // Combine ripples for more organic appearance
    float combinedRipple = baseRipple + secondaryRipple;

    // Apply smoothing to create more organic, cloud-like transitions
    // This creates a more natural, less mechanical appearance
    float organicRipple = combinedRipple * (1.0 - 0.3 * abs(combinedRipple));

    // Apply a gradual falloff based on distance from center
    float falloff = 1.0 - smoothstep(0.0, 0.8, distance);
    falloff = 0.3 + 0.7 * falloff; // Keep minimum 0.3 intensity even at edges for more visible ripples

    // Add noise-like variation for more organic appearance
    float noiseVariation = sin(position.x * 12.0 + position.y * 15.0 + time) * 0.2;

    // Apply pole protection - reduce effect near poles (y close to 0 or 1)
    float poleDistance = min(position.y, 1.0 - position.y); // Distance from either pole
    float poleFalloff = smoothstep(0.0, 0.25, poleDistance); // Stronger falloff near poles

    // Combine everything for final organic ripple effect with pole protection
    float organicEffect = amplitude * (organicRipple + noiseVariation) * falloff * poleFalloff;

    // Apply a soft limit to prevent extreme values while allowing pronounced ripples
    float clampedEffect = clamp(organicEffect, -amplitude * 0.9, amplitude * 0.9);

    // Apply additional smoothing at the transition points to prevent any sharp changes
    // This helps prevent tearing by ensuring smooth transitions at the edges of the effect
    return clampedEffect * (1.0 - 0.1 * pow(abs(clampedEffect / amplitude), 2.0));
  }

  void main() {
    vUv = uv;
    vNormal = normalize(normalMatrix * normal);
    vOrigPosition = position;

    // Calculate latitude and longitude for this vertex
    vec3 normalized = normalize(position);
    float lat = acos(normalized.y) / 3.14159;
    float lon = atan(normalized.z, normalized.x) / (2.0 * 3.14159) + 0.5;

    // Determine if we're in an active call
    bool isActive = isSpeaking > 0.5 || isListening > 0.5;

    // Calculate displacement
    float displacement = 0.0;

    if (!isActive) {
      // Not in call: use a uniform breathing effect
      float breathingAmplitude = 0.5;
      float breathingSpeed = 0.3;
      displacement = breathingAmplitude * sin(time * breathingSpeed);
    } else {
      // In call: apply ripple effects
      float rippleIntensity;
      float rippleSpeed;

      if (isSpeaking > 0.5 && isListening > 0.5) {
        // Both active: maximum ripple
        rippleIntensity = 5.0;
        rippleSpeed = 2.5;
      } else if (isSpeaking > 0.5) {
        // Agent speaking: high ripple
        rippleIntensity = 4.5;
        rippleSpeed = 2.2;
      } else {
        // User speaking: medium ripple
        rippleIntensity = 4.0;
        rippleSpeed = 2.0;
      }

      // Create ripple centers that move over time but strictly avoid the pole regions
      // Keep ripple centers away from the poles by limiting their latitude range
      // Use a narrower range (0.3-0.7) to keep ripples away from pole regions
      vec2 rippleCenter1 = vec2(
        0.5 + 0.2 * sin(time * 0.3), // Reduced amplitude
        0.5 + 0.2 * cos(time * 0.4) * 0.8 // Keep centers further from poles (0.3-0.7 range)
      );

      vec2 rippleCenter2 = vec2(
        0.5 + 0.2 * cos(time * 0.5), // Reduced amplitude
        0.5 + 0.2 * sin(time * 0.6) * 0.8 // Keep centers further from poles (0.3-0.7 range)
      );

      // Ensure ripple centers never get too close to poles by clamping latitude values
      rippleCenter1.y = clamp(rippleCenter1.y, 0.3, 0.7); // Restrict to middle 40% of sphere
      rippleCenter2.y = clamp(rippleCenter2.y, 0.3, 0.7); // Restrict to middle 40% of sphere

      // Calculate distance from vertex to ripple centers
      float dist1 = sqrt(pow(lat - rippleCenter1.x, 2.0) + pow(lon - rippleCenter1.y, 2.0));
      float dist2 = sqrt(pow(lat - rippleCenter2.x, 2.0) + pow(lon - rippleCenter2.y, 2.0));

      // Create ripple effects with the improved ripple function
      float ripple1 = ripple(vec2(lat, lon), rippleCenter1, time,
                            10.0 + bassIntensity * 15.0,
                            3.0 + bassIntensity * 4.0,
                            (0.3 + bassIntensity * 0.7) * rippleIntensity);

      float ripple2 = ripple(vec2(lat, lon), rippleCenter2, time,
                            8.0 + midIntensity * 12.0,
                            2.5 + midIntensity * 3.0,
                            (0.2 + midIntensity * 0.8) * rippleIntensity);

      // Create smoother wave patterns using cubic interpolation
      // Base sine waves
      float sineWave1 = sin(time * rippleSpeed + lat * 5.0);
      float sineWave2 = sin(time * rippleSpeed * 0.7 + lon * 4.0);
      float sineWave3 = sin(time * rippleSpeed * 1.3 + (lat + lon) * 3.0);

      // Apply cubic smoothing to the waves (similar to smoothstep)
      float smoothWave1 = sineWave1 * sineWave1 * (3.0 - 2.0 * abs(sineWave1));
      float smoothWave2 = sineWave2 * sineWave2 * (3.0 - 2.0 * abs(sineWave2));
      float smoothWave3 = sineWave3 * sineWave3 * (3.0 - 2.0 * abs(sineWave3));

      // Apply audio intensity
      float wave1 = smoothWave1 * bassIntensity * rippleIntensity * 1.8;
      float wave2 = smoothWave2 * midIntensity * rippleIntensity * 1.8;
      float wave3 = smoothWave3 * trebleIntensity * rippleIntensity * 1.8;

      // Combine waves and ripples with gradual blending
      float rippleWave1 = ripple1 * bassIntensity * rippleIntensity;
      float rippleWave2 = ripple2 * midIntensity * rippleIntensity;

      // Create more pronounced organic ripples like in the reference image
      // Increase the weight of ripple effects while keeping wave effects for variety
      float totalEffect = wave1 * 0.8 + wave2 * 0.8 + wave3 * 0.8 + rippleWave1 * 1.2 + rippleWave2 * 1.2;

      // Add noise-based displacement for more organic, cloud-like appearance
      float noiseDisplacement = sin(lat * 15.0 + lon * 12.0 + time * 0.5) * 0.5 +
                               cos(lat * 12.0 - lon * 15.0 + time * 0.7) * 0.5;

      // Combine with noise for more organic appearance
      totalEffect = totalEffect + noiseDisplacement * bassIntensity * 2.0;

      // Apply a soft clamp using smoothstep for more gradual limiting
      // Use a wider range to allow more pronounced ripples while still preventing extreme values
      float softClampedEffect = 5.0 * smoothstep(0.0, 1.0, (totalEffect + 5.0) / 10.0) - 5.0;

      // Apply a smoothing function that preserves medium-sized ripples but prevents sharp edges
      float smoothedEffect = softClampedEffect * (1.0 - 0.15 * pow(abs(softClampedEffect / 5.0), 2.0));

      // Apply a stronger spherical falloff specifically near the poles to prevent any tearing
      // More aggressive near the poles (lat close to 0 or 1) but gentler elsewhere
      float poleDistance = min(lat, 1.0 - lat); // Distance from either pole (0 at poles, 0.5 at equator)
      float poleFalloff = smoothstep(0.0, 0.2, poleDistance); // Smooth transition from poles

      // Apply additional smoothing very close to the poles to ensure no discontinuities
      if (poleDistance < 0.1) {
        // Within 10% of the poles, apply very strong reduction
        float poleReduction = smoothstep(0.0, 0.1, poleDistance); // Even smoother very close to poles
        poleFalloff *= poleReduction * poleReduction; // Square it for stronger effect
      }

      // Combine with a gentler overall latitude falloff
      float latitudeFalloff = poleFalloff * (0.7 + 0.3 * (1.0 - 0.5 * pow(abs(2.0 * lat - 1.0), 2.0)));

      // Final displacement with enhanced ripples but still maintaining blob cohesion
      displacement = smoothedEffect * latitudeFalloff;
    }

    // Apply displacement along the normal vector (radial direction)
    vec3 newPosition = position + normalized * displacement;
    vPosition = newPosition;

    gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
  }
`;

// Create fragment shader with enhanced multi-color patterns
const fragmentShader = `
  uniform float time;
  uniform float bassIntensity;
  uniform float midIntensity;
  uniform float trebleIntensity;
  uniform float isSpeaking;
  uniform float isListening;
  uniform float audioState;

  // Default palette (inactive) - now with 7 colors
  uniform vec3 color1;
  uniform vec3 color2;
  uniform vec3 color3;
  uniform vec3 color4;
  uniform vec3 color5;
  uniform vec3 color6;
  uniform vec3 color7;

  // User speaking palette (blue/cyan)
  uniform vec3 userColor1;
  uniform vec3 userColor2;
  uniform vec3 userColor3;
  uniform vec3 userColor4;
  uniform vec3 userColor5;

  // Agent speaking palette (purple/pink) - now with 7 colors
  uniform vec3 agentColor1;
  uniform vec3 agentColor2;
  uniform vec3 agentColor3;
  uniform vec3 agentColor4;
  uniform vec3 agentColor5;
  uniform vec3 agentColor6;
  uniform vec3 agentColor7;

  // Both speaking palette (yellow/orange/red)
  uniform vec3 bothColor1;
  uniform vec3 bothColor2;
  uniform vec3 bothColor3;
  uniform vec3 bothColor4;
  uniform vec3 bothColor5;
  varying vec2 vUv;
  varying vec3 vNormal;
  varying vec3 vPosition;
  varying vec3 vOrigPosition;

  // Function to create smooth transitions between colors
  vec3 lerpColor(vec3 a, vec3 b, float t) {
    return a + (b - a) * t;
  }

  // Function to create smoother transitions with cubic interpolation
  float smoothStep(float edge0, float edge1, float x) {
    float t = clamp((x - edge0) / (edge1 - edge0), 0.0, 1.0);
    return t * t * (3.0 - 2.0 * t);
  }

  // Function to create a noise-like pattern for more organic color transitions
  float noise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);

    // Four corners in 2D of a tile
    float a = sin(i.x + i.y * 13.0) * 43758.5453123;
    float b = sin(i.x + 1.0 + i.y * 13.0) * 43758.5453123;
    float c = sin(i.x + i.y * 13.0 + 13.0) * 43758.5453123;
    float d = sin(i.x + 1.0 + i.y * 13.0 + 13.0) * 43758.5453123;

    // Smooth interpolation
    vec2 u = f * f * (3.0 - 2.0 * f);

    // Mix 4 corners
    return mix(
      mix(fract(a), fract(b), u.x),
      mix(fract(c), fract(d), u.x),
      u.y
    );
  }

  // Function to create organic, cloud-like ripple effect similar to the reference image
  // with additional pole protection to prevent tearing
  float ripple(vec2 position, vec2 center, float time, float frequency, float speed, float amplitude) {
    float distance = length(position - center);

    // Create base ripple effect with sine wave
    float baseRipple = sin(distance * frequency - time * speed);

    // Add secondary ripple with different frequency for more complex patterns
    float secondaryRipple = 0.5 * sin(distance * frequency * 1.5 - time * speed * 0.8);

    // Combine ripples for more organic appearance
    float combinedRipple = baseRipple + secondaryRipple;

    // Apply smoothing to create more organic, cloud-like transitions
    // This creates a more natural, less mechanical appearance
    float organicRipple = combinedRipple * (1.0 - 0.3 * abs(combinedRipple));

    // Apply a gradual falloff based on distance from center
    float falloff = 1.0 - smoothstep(0.0, 0.8, distance);
    falloff = 0.3 + 0.7 * falloff; // Keep minimum 0.3 intensity even at edges for more visible ripples

    // Add noise-like variation for more organic appearance
    float noiseVariation = sin(position.x * 12.0 + position.y * 15.0 + time) * 0.2;

    // Apply pole protection - reduce effect near poles (y close to 0 or 1)
    float poleDistance = min(position.y, 1.0 - position.y); // Distance from either pole
    float poleFalloff = smoothstep(0.0, 0.25, poleDistance); // Stronger falloff near poles

    // Combine everything for final organic ripple effect with pole protection
    float organicEffect = amplitude * (organicRipple + noiseVariation) * falloff * poleFalloff;

    // Apply a soft limit to prevent extreme values while allowing pronounced ripples
    float clampedEffect = clamp(organicEffect, -amplitude * 0.9, amplitude * 0.9);

    // Apply additional smoothing at the transition points to prevent any sharp changes
    // This helps prevent tearing by ensuring smooth transitions at the edges of the effect
    return clampedEffect * (1.0 - 0.1 * pow(abs(clampedEffect / amplitude), 2.0));
  }

  void main() {
    // Determine audio state: 0=inactive, 1=listening, 2=speaking, 3=both
    float audioState = 0.0;
    if (isListening > 0.5 && isSpeaking > 0.5) {
      audioState = 3.0; // Both active
    } else if (isSpeaking > 0.5) {
      audioState = 2.0; // Agent speaking
    } else if (isListening > 0.5) {
      audioState = 1.0; // User speaking
    }

    // Create dynamic speed based on audio state
    float speed = 1.0;
    if (audioState > 0.0) {
      // Base speed increase when any audio is active
      speed = 1.2;

      // Additional speed adjustments based on specific audio state
      if (audioState == 1.0) {
        // User speaking - slightly faster
        speed = 1.3;
      } else if (audioState == 2.0) {
        // Agent speaking - even faster
        speed = 1.5;
      } else if (audioState == 3.0) {
        // Both active - fastest
        speed = 1.7;
      }
    }

    // Use position and normal for more interesting patterns
    float lat = acos(vNormal.y) / 3.14159;
    float lon = atan(vNormal.z, vNormal.x) / (2.0 * 3.14159) + 0.5;

    // Create noise-based coordinates for more organic patterns
    // Adjust noise pattern based on audio state
    float noiseSpeed = speed * (1.0 + 0.2 * audioState);
    vec2 noiseCoord = vec2(
      lon * 10.0 + time * noiseSpeed * 0.2,
      lat * 10.0 + time * noiseSpeed * 0.3
    );
    float noisePattern = noise(noiseCoord) * 2.0 - 1.0;

    // Create ripple centers that move over time but strictly avoid the pole regions
    // Keep ripple centers away from the poles by limiting their latitude range
    // Use a narrower range (0.3-0.7) to keep ripples away from pole regions
    vec2 rippleCenter1 = vec2(
      0.5 + 0.2 * sin(time * 0.3), // Reduced amplitude
      0.5 + 0.2 * cos(time * 0.4) * 0.8 // Keep centers further from poles (0.3-0.7 range)
    );

    vec2 rippleCenter2 = vec2(
      0.5 + 0.2 * cos(time * 0.5), // Reduced amplitude
      0.5 + 0.2 * sin(time * 0.6) * 0.8 // Keep centers further from poles (0.3-0.7 range)
    );

    vec2 rippleCenter3 = vec2(
      0.5 + 0.2 * sin(time * 0.7 + 1.0), // Reduced amplitude
      0.5 + 0.2 * cos(time * 0.8 + 1.0) * 0.8 // Keep centers further from poles (0.3-0.7 range)
    );

    // Ensure ripple centers never get too close to poles by clamping latitude values
    rippleCenter1.y = clamp(rippleCenter1.y, 0.3, 0.7); // Restrict to middle 40% of sphere
    rippleCenter2.y = clamp(rippleCenter2.y, 0.3, 0.7); // Restrict to middle 40% of sphere
    rippleCenter3.y = clamp(rippleCenter3.y, 0.3, 0.7); // Restrict to middle 40% of sphere

    // Create dynamic ripple effects based on audio intensity
    float rippleFreq1 = 10.0 + bassIntensity * 15.0;
    float rippleFreq2 = 8.0 + midIntensity * 12.0;
    float rippleFreq3 = 12.0 + trebleIntensity * 18.0;

    float rippleSpeed1 = 3.0 + bassIntensity * 4.0;
    float rippleSpeed2 = 2.5 + midIntensity * 3.0;
    float rippleSpeed3 = 4.0 + trebleIntensity * 5.0;

    float rippleAmp1 = 0.3 + bassIntensity * 0.7;
    float rippleAmp2 = 0.2 + midIntensity * 0.8;
    float rippleAmp3 = 0.25 + trebleIntensity * 0.75;

    // Generate ripple effects with improved smooth ripple function
    float rippleEffect1 = ripple(vec2(lat, lon), rippleCenter1, time, rippleFreq1, rippleSpeed1, rippleAmp1);
    float rippleEffect2 = ripple(vec2(lat, lon), rippleCenter2, time, rippleFreq2, rippleSpeed2, rippleAmp2);
    float rippleEffect3 = ripple(vec2(lat, lon), rippleCenter3, time, rippleFreq3, rippleSpeed3, rippleAmp3);

    // Apply additional smoothing to ripple transitions for color effects
    // This ensures color transitions are even smoother than displacement effects
    rippleEffect1 = smoothStep(0.0, 1.0, (rippleEffect1 + 1.0) * 0.5) * 2.0 - 1.0;
    rippleEffect2 = smoothStep(0.0, 1.0, (rippleEffect2 + 1.0) * 0.5) * 2.0 - 1.0;
    rippleEffect3 = smoothStep(0.0, 1.0, (rippleEffect3 + 1.0) * 0.5) * 2.0 - 1.0;

    // Create smoother wave patterns with cubic interpolation
    // First create base sine waves
    float sineWave1 = sin(lat * 5.0 + time * speed + lon * 3.0 + rippleEffect1);
    float sineWave2 = sin(lon * 7.0 + time * speed * 1.3 + lat * 2.0 + rippleEffect2);
    float sineWave3 = sin((lat + lon) * 6.0 + time * speed * 0.7 + rippleEffect3);
    float sineWave4 = sin(lat * 8.0 - lon * 4.0 + time * speed * 0.9 + rippleEffect1 * rippleEffect2);
    float sineWave5 = cos(lon * 9.0 - lat * 5.0 + time * speed * 1.1 + rippleEffect2 * rippleEffect3);

    // Apply cubic smoothing to create smoother transitions at peaks and troughs
    float smoothWave1 = sineWave1 * sineWave1 * (3.0 - 2.0 * abs(sineWave1));
    float smoothWave2 = sineWave2 * sineWave2 * (3.0 - 2.0 * abs(sineWave2));
    float smoothWave3 = sineWave3 * sineWave3 * (3.0 - 2.0 * abs(sineWave3));
    float smoothWave4 = sineWave4 * sineWave4 * (3.0 - 2.0 * abs(sineWave4));
    float smoothWave5 = sineWave5 * sineWave5 * (3.0 - 2.0 * abs(sineWave5));

    // Combine with audio intensity for final wave values
    float wave1 = 0.5 + 0.5 * smoothWave1 * (0.7 + bassIntensity * 0.7);
    float wave2 = 0.5 + 0.5 * smoothWave2 * (0.7 + midIntensity * 0.7);
    float wave3 = 0.5 + 0.5 * smoothWave3 * (0.7 + trebleIntensity * 0.7);
    float wave4 = 0.5 + 0.5 * smoothWave4 * (0.7 + bassIntensity * 0.8);
    float wave5 = 0.5 + 0.5 * smoothWave5 * (0.7 + midIntensity * 0.8);

    // Create swirling patterns with more complexity and audio reactivity
    // Base swirl calculations
    float baseSwirlA1 = sin(10.0 * lat + time * (0.5 + bassIntensity * 0.5));
    float baseSwirlA2 = cos(10.0 * lon + time * (0.3 + midIntensity * 0.4));
    float baseSwirlB1 = sin(8.0 * lon + time * (0.7 + midIntensity * 0.6));
    float baseSwirlB2 = cos(8.0 * lat - time * (0.2 + bassIntensity * 0.5));
    float baseSwirlC1 = sin(12.0 * lat - time * (0.4 + trebleIntensity * 0.5));
    float baseSwirlC2 = cos(6.0 * lon + time * (0.6 + bassIntensity * 0.4));

    // Apply cubic smoothing to each component
    float smoothSwirlA1 = baseSwirlA1 * baseSwirlA1 * (3.0 - 2.0 * abs(baseSwirlA1));
    float smoothSwirlA2 = baseSwirlA2 * baseSwirlA2 * (3.0 - 2.0 * abs(baseSwirlA2));
    float smoothSwirlB1 = baseSwirlB1 * baseSwirlB1 * (3.0 - 2.0 * abs(baseSwirlB1));
    float smoothSwirlB2 = baseSwirlB2 * baseSwirlB2 * (3.0 - 2.0 * abs(baseSwirlB2));
    float smoothSwirlC1 = baseSwirlC1 * baseSwirlC1 * (3.0 - 2.0 * abs(baseSwirlC1));
    float smoothSwirlC2 = baseSwirlC2 * baseSwirlC2 * (3.0 - 2.0 * abs(baseSwirlC2));

    // Combine components with smooth transitions
    float swirl1 = smoothSwirlA1 * smoothSwirlA2;
    float swirl2 = smoothSwirlB1 * smoothSwirlB2;
    float swirl3 = smoothSwirlC1 * smoothSwirlC2;

    // Create spiral patterns that rotate around the sphere with audio reactivity
    // Base spiral calculations
    float baseSpiral1 = sin(lon * (15.0 + bassIntensity * 5.0) + lat * (10.0 + midIntensity * 4.0) + time * speed);
    float baseSpiral2 = cos(lat * (12.0 + midIntensity * 6.0) - lon * (8.0 + trebleIntensity * 4.0) + time * speed * 0.8);

    // Apply cubic smoothing for smoother transitions
    float smoothSpiral1 = baseSpiral1 * baseSpiral1 * (3.0 - 2.0 * abs(baseSpiral1));
    float smoothSpiral2 = baseSpiral2 * baseSpiral2 * (3.0 - 2.0 * abs(baseSpiral2));

    // Final spiral values with normalized range
    float spiral1 = 0.5 + 0.5 * smoothSpiral1;
    float spiral2 = 0.5 + 0.5 * smoothSpiral2;

    // Create radial patterns emanating from poles with audio reactivity
    // Calculate distances for radial patterns
    float dist1 = distance(vec2(lat, lon), vec2(0.5, 0.5));
    float dist2 = distance(vec2(lat, lon), vec2(0.0, 0.0));

    // Base radial calculations
    float baseRadial1 = sin((20.0 + bassIntensity * 10.0) * dist1 - time * speed);
    float baseRadial2 = cos((15.0 + midIntensity * 8.0) * dist2 + time * speed * 1.2);

    // Apply cubic smoothing for smoother transitions
    float smoothRadial1 = baseRadial1 * baseRadial1 * (3.0 - 2.0 * abs(baseRadial1));
    float smoothRadial2 = baseRadial2 * baseRadial2 * (3.0 - 2.0 * abs(baseRadial2));

    // Apply gradual falloff based on distance from center
    float radialFalloff1 = 1.0 - smoothstep(0.0, 0.8, dist1);
    float radialFalloff2 = 1.0 - smoothstep(0.0, 0.8, dist2);
    radialFalloff1 = 0.3 + 0.7 * radialFalloff1; // Keep minimum 0.3 intensity even at edges
    radialFalloff2 = 0.3 + 0.7 * radialFalloff2;

    // Final radial values with normalized range and falloff
    float radial1 = 0.5 + 0.5 * smoothRadial1 * radialFalloff1;
    float radial2 = 0.5 + 0.5 * smoothRadial2 * radialFalloff2;

    // Create stronger audio-reactive patterns with smooth transitions
    // Base audio patterns
    float baseAudioPulse = sin(time * (1.0 + bassIntensity * 2.0 + midIntensity * 1.5));
    float baseAudioWave = sin(lat * (10.0 + bassIntensity * 8.0) * (1.0 + trebleIntensity * 1.5) + time * speed);

    // Apply cubic smoothing for smoother transitions
    float smoothAudioPulse = baseAudioPulse * baseAudioPulse * (3.0 - 2.0 * abs(baseAudioPulse));
    float smoothAudioWave = baseAudioWave * baseAudioWave * (3.0 - 2.0 * abs(baseAudioWave));

    // Final audio values with normalized range
    float audioPulse = 0.5 + 0.5 * smoothAudioPulse;
    float audioWave = 0.5 + 0.5 * smoothAudioWave;

    // Combine patterns with varying weights for more complex interactions
    float pattern1 = wave1 * 0.4 + swirl1 * 0.3 + spiral1 * 0.3;
    float pattern2 = wave2 * 0.35 + swirl2 * 0.35 + radial1 * 0.3;
    float pattern3 = wave3 * 0.3 + swirl3 * 0.3 + spiral2 * 0.4;
    float pattern4 = wave4 * 0.4 + radial2 * 0.3 + noisePattern * 0.3;
    float pattern5 = wave5 * 0.3 + audioPulse * 0.3 + audioWave * 0.4;

    // Create dynamic pattern blending based on audio and time
    float blendFactor1 = smoothStep(0.0, 1.0, 0.5 + 0.5 * sin(time * 0.3));
    float blendFactor2 = smoothStep(0.0, 1.0, 0.5 + 0.5 * cos(time * 0.4));
    float blendFactor3 = smoothStep(0.0, 1.0, 0.5 + 0.5 * sin(time * 0.5 + 2.0));
    float audioBlend = smoothStep(0.0, 1.0, (bassIntensity + midIntensity) * 0.5);

    // Select the appropriate color palette based on audio state
    // Using arrays to handle different palette sizes
    vec3 colors[7]; // Max 7 colors
    int colorCount;

    // Use audioState uniform for more precise control
    if (audioState < 0.5) {
        // Inactive - default palette (7 colors)
        colors[0] = color1;
        colors[1] = color2;
        colors[2] = color3;
        colors[3] = color4;
        colors[4] = color5;
        colors[5] = color6;
        colors[6] = color7;
        colorCount = 7;
    } else if (audioState < 1.5) {
        // User speaking - blue/cyan palette (5 colors)
        colors[0] = userColor1;
        colors[1] = userColor2;
        colors[2] = userColor3;
        colors[3] = userColor4;
        colors[4] = userColor5;
        colorCount = 5;
    } else if (audioState < 2.5) {
        // Agent speaking - purple/pink palette (7 colors)
        colors[0] = agentColor1;
        colors[1] = agentColor2;
        colors[2] = agentColor3;
        colors[3] = agentColor4;
        colors[4] = agentColor5;
        colors[5] = agentColor6;
        colors[6] = agentColor7;
        colorCount = 7;
    } else {
        // Both speaking - yellow/orange/red palette (5 colors)
        colors[0] = bothColor1;
        colors[1] = bothColor2;
        colors[2] = bothColor3;
        colors[3] = bothColor4;
        colors[4] = bothColor5;
        colorCount = 5;
    }

    // Create multi-stage color transitions with all available colors
    // First stage: Create intermediate colors from the base colors
    vec3 mixColor1 = lerpColor(colors[0], colors[1], pattern1);
    vec3 mixColor2 = lerpColor(colors[1], colors[2], pattern2);
    vec3 mixColor3 = lerpColor(colors[2], colors[3], pattern3);
    vec3 mixColor4, mixColor5, mixColor6, mixColor7;

    // Handle different color counts
    if (colorCount >= 5) {
        mixColor4 = lerpColor(colors[3], colors[4], pattern4);
        mixColor5 = lerpColor(colors[4], colors[0], pattern5);
    } else {
        // Fallback for fewer colors
        mixColor4 = lerpColor(colors[3], colors[0], pattern4);
        mixColor5 = lerpColor(colors[0], colors[1], pattern5);
    }

    // Add extra color mixes for palettes with more colors
    if (colorCount >= 7) {
        mixColor6 = lerpColor(colors[5], colors[6], 0.5 + 0.5 * sin(time * 0.6));
        mixColor7 = lerpColor(colors[6], colors[0], 0.5 + 0.5 * cos(time * 0.7));
    } else {
        // Fallbacks for smaller palettes
        mixColor6 = lerpColor(mixColor1, mixColor3, 0.5 + 0.5 * sin(time * 0.6));
        mixColor7 = lerpColor(mixColor2, mixColor4, 0.5 + 0.5 * cos(time * 0.7));
    }

    // Second stage: Create more complex color blends with additional colors
    vec3 blendA = lerpColor(mixColor1, mixColor3, blendFactor1);
    vec3 blendB = lerpColor(mixColor2, mixColor4, blendFactor2);
    vec3 blendC = lerpColor(mixColor5, mixColor1, blendFactor3);

    // Add new blends with the additional colors
    float blendFactor4 = smoothStep(0.0, 1.0, 0.5 + 0.5 * sin(time * 0.6 + 1.0));
    float blendFactor5 = smoothStep(0.0, 1.0, 0.5 + 0.5 * cos(time * 0.7 + 1.0));

    vec3 blendD = lerpColor(mixColor6, mixColor2, blendFactor4);
    vec3 blendE = lerpColor(mixColor7, mixColor4, blendFactor5);

    // Third stage: Create dynamic region-based color mixing
    // Use latitude bands for horizontal color regions
    float latBand = smoothStep(0.0, 1.0, abs(sin(lat * 3.14159 * 3.0 + time * 0.2)));

    // Use longitude bands for vertical color regions
    float lonBand = smoothStep(0.0, 1.0, abs(sin(lon * 3.14159 * 4.0 + time * 0.3)));

    // Use diagonal bands for diagonal color regions
    float diagBand = smoothStep(0.0, 1.0, abs(sin((lat + lon) * 3.14159 * 2.0 + time * 0.4)));

    // Use radial bands for circular color regions
    float radialBand = smoothStep(0.0, 1.0, abs(sin(distance(vec2(lat, lon), vec2(0.5, 0.5)) * 10.0 + time * 0.5)));

    // Combine bands with noise for organic region boundaries
    float regionMix1 = latBand * 0.4 + lonBand * 0.3 + noisePattern * 0.3;
    float regionMix2 = lonBand * 0.3 + diagBand * 0.4 + noisePattern * 0.3;
    float regionMix3 = diagBand * 0.3 + radialBand * 0.4 + noisePattern * 0.3;
    float regionMix4 = radialBand * 0.4 + latBand * 0.3 + noisePattern * 0.3;
    float regionMix5 = diagBand * 0.4 + lonBand * 0.3 + noisePattern * 0.3;

    // Final multi-region color blending with additional colors
    vec3 regionColor1 = lerpColor(blendA, blendB, regionMix1);
    vec3 regionColor2 = lerpColor(blendB, blendC, regionMix2);
    vec3 regionColor3 = lerpColor(blendC, blendA, regionMix3);
    vec3 regionColor4 = lerpColor(blendD, blendE, regionMix4);
    vec3 regionColor5 = lerpColor(blendE, blendA, regionMix5);

    // Dynamic final color mixing based on audio intensity
    float finalMix1 = 0.5 + 0.5 * sin(time * 0.7 + lat * 2.0);
    float finalMix2 = 0.5 + 0.5 * cos(time * 0.5 + lon * 3.0);

    // Create stronger audio-reactive final mix with ripple influence
    float audioReactiveMix = bassIntensity * 0.8 + midIntensity * 0.6 + trebleIntensity * 0.5;

    // Add ripple effects to the audio mix for more dynamic color changes
    float rippleInfluence = abs(rippleEffect1 + rippleEffect2 + rippleEffect3) * 0.2;
    audioReactiveMix = audioReactiveMix * (1.0 + rippleInfluence);

    float finalAudioMix = smoothStep(0.0, 1.0, audioReactiveMix);

    // Final color with multi-stage blending and audio reactivity
    float audioBlendFactor = 0.5 + 0.5 * sin(time * (1.0 + bassIntensity * 1.5));
    float finalMix3 = 0.5 + 0.5 * sin(time * 0.6 + (lat + lon) * 2.5);
    float finalMix4 = 0.5 + 0.5 * cos(time * 0.8 + lat * lon * 3.0);

    // Create more complex color mixing with the additional region colors
    vec3 tempColor1 = lerpColor(regionColor1, regionColor2, finalMix1 * (1.0 + midIntensity * 0.5));
    vec3 tempColor2 = lerpColor(regionColor3, regionColor4, finalMix2 * (1.0 + trebleIntensity * 0.5));
    vec3 tempColor3 = lerpColor(tempColor1, tempColor2, finalMix3 * (1.0 + bassIntensity * 0.6));
    vec3 finalColor = lerpColor(tempColor3, regionColor5, finalMix4 * (1.0 + (bassIntensity + midIntensity) * 0.4));

    // Apply audio-reactive color shift based on audio state
    if (audioState > 0.0) {
      // Base audio-reactive color shift
      vec3 audioShiftColor;
      float audioShiftIntensity;

      if (audioState == 1.0) {
        // User speaking (microphone) - shift toward cyan/blue tones with ripple effects
        // Create a cooler, more blue-dominant color shift with increased reactivity

        // Use ripple effects to create more dynamic color shifts
        float userRippleMix = audioReactiveMix * 1.5 * (1.0 + abs(rippleEffect1) * 0.5);

        // Create more complex color transitions with ripple-influenced mixing
        vec3 baseColor = lerpColor(mixColor1, mixColor2, userRippleMix);
        vec3 accentColor = lerpColor(mixColor2, mixColor3, userRippleMix * 0.8);

        // Blend based on ripple pattern for more dynamic effects
        float rippleBlend = 0.5 + 0.5 * sin(rippleEffect2 * 3.0 + time);
        audioShiftColor = lerpColor(baseColor, accentColor, rippleBlend);

        // Increase intensity with ripple influence
        audioShiftIntensity = finalAudioMix * 0.9 * (1.0 + abs(rippleEffect3) * 0.3);
      }
      else if (audioState == 2.0) {
        // Agent speaking - shift toward purple/magenta tones with ripple effects
        // Create a stronger, more purple-dominant color shift with increased reactivity

        // Use ripple effects to create more dynamic color shifts with higher intensity
        float agentRippleMix = audioReactiveMix * 2.0 * (1.0 + abs(rippleEffect2) * 0.8);

        // Force more purple colors by directly using the agent palette colors
        // Instead of using mixColors which might dilute the purples
        vec3 purpleBase = colors[1]; // Violet
        vec3 purpleAccent1 = colors[3]; // Purple
        vec3 purpleAccent2 = colors[5]; // Bright Purple
        vec3 purpleAccent3 = colors[0]; // Dark Violet
        vec3 purpleAccent4 = colors[4]; // Bright Purple

        // Create dynamic blending between pure purple colors
        float purpleBlend1 = 0.5 + 0.5 * sin(rippleEffect1 * 5.0 + time * 1.5);
        float purpleBlend2 = 0.5 + 0.5 * cos(rippleEffect2 * 4.5 + time * 1.2);
        float purpleBlend3 = 0.5 + 0.5 * sin(rippleEffect3 * 4.0 + time * 1.0);

        // Create multiple layers of purple blends
        vec3 purpleMix1 = lerpColor(purpleBase, purpleAccent1, purpleBlend1);
        vec3 purpleMix2 = lerpColor(purpleAccent2, purpleAccent3, purpleBlend2);
        vec3 purpleMix3 = lerpColor(purpleMix1, purpleMix2, purpleBlend3);

        // Add a final blend with another accent for more variety
        float finalPurpleBlend = 0.5 + 0.5 * sin(time * 1.8 + rippleEffect1 * rippleEffect2);
        audioShiftColor = lerpColor(purpleMix3, purpleAccent4, finalPurpleBlend);

        // Boost saturation of purple colors
        float purpleSaturation = 1.3; // Increase saturation
        float maxChannel = max(audioShiftColor.r, max(audioShiftColor.g, audioShiftColor.b));

        // If blue or red is dominant (which makes purple), boost it further
        if (maxChannel == audioShiftColor.r || maxChannel == audioShiftColor.b) {
          audioShiftColor.r = min(1.0, audioShiftColor.r * purpleSaturation);
          audioShiftColor.b = min(1.0, audioShiftColor.b * purpleSaturation);
          // Reduce green to make purple more vibrant
          audioShiftColor.g *= 0.7;
        }

        // Increase intensity with ripple influence - higher value for more impact
        audioShiftIntensity = finalAudioMix * 1.3 * (1.0 + abs(rippleEffect3) * 0.6);
      }
      else if (audioState == 3.0) {
        // Both active - create a dynamic blend of both color shifts with ripple effects
        // This creates a more complex, vibrant pattern when both are active

        // Use ripple effects to create more dynamic color shifts
        float bothRippleMix1 = audioReactiveMix * 1.7 * (1.0 + abs(rippleEffect1) * 0.7);
        float bothRippleMix2 = audioReactiveMix * 1.8 * (1.0 + abs(rippleEffect2) * 0.8);

        // Create more complex color transitions with ripple-influenced mixing
        vec3 userColor = lerpColor(mixColor1, mixColor2, bothRippleMix1);
        vec3 agentColor = lerpColor(mixColor3, mixColor5, bothRippleMix2);
        vec3 accentColor = lerpColor(mixColor4, mixColor5, bothRippleMix1 * bothRippleMix2 * 0.5);

        // Create a more dynamic pulsing blend between user and agent colors with ripple influence
        float blendPulse = 0.5 + 0.5 * sin(time * (2.5 + bassIntensity * 2.5) + rippleEffect3 * 5.0);
        vec3 baseBlend = lerpColor(userColor, agentColor, blendPulse);

        // Add accent color with ripple-based mixing
        float accentBlend = 0.5 + 0.5 * cos(time * 1.5 + rippleEffect1 * rippleEffect2 * 3.0);
        audioShiftColor = lerpColor(baseBlend, accentColor, accentBlend * 0.4);

        // Much higher intensity when both are active, with ripple influence
        audioShiftIntensity = finalAudioMix * 1.2 * (1.0 + abs(rippleEffect1 + rippleEffect2 + rippleEffect3) * 0.2);
      }

      // Apply the audio-reactive color shift
      finalColor = lerpColor(finalColor, audioShiftColor, audioShiftIntensity);
    }

    // Apply enhanced intensity boost based on audio state
    float intensityBoost = 1.0;

    if (audioState == 0.0) {
      // Inactive - subtle pulsing
      intensityBoost = 1.1 + 0.2 * abs(sin(time * 0.7 + lon * 2.0));
    }
    else if (audioState == 1.0) {
      // User speaking - stronger intensity boost with position-based variation and ripple effects
      intensityBoost = 1.3 + 0.4 * abs(sin(time * 0.9 + lat * 2.5 + rippleEffect1));

      // Add audio-reactive intensity variation with ripple influence
      intensityBoost += bassIntensity * 0.3 + midIntensity * 0.25 + abs(rippleEffect2) * 0.3;

      // Add ripple-based color pulsing
      intensityBoost *= 1.0 + 0.2 * abs(rippleEffect3);
    }
    else if (audioState == 2.0) {
      // Agent speaking - high intensity boost with ripple effects
      intensityBoost = 1.4 + 0.5 * abs(sin(time + lat * 2.0 + rippleEffect2));

      // Add audio-reactive intensity variation with ripple influence
      intensityBoost += bassIntensity * 0.35 + trebleIntensity * 0.3 + abs(rippleEffect1) * 0.35;

      // Add ripple-based color pulsing
      intensityBoost *= 1.0 + 0.25 * abs(rippleEffect3);
    }
    else if (audioState == 3.0) {
      // Both active - maximum intensity boost with complex pattern and ripple effects
      intensityBoost = 1.5 + 0.6 * abs(sin(time * 1.2 + lat * 3.0 + rippleEffect1)) * abs(cos(time * 0.8 + lon * 2.5 + rippleEffect2));

      // Add strong audio-reactive intensity variation with ripple influence
      intensityBoost += bassIntensity * 0.4 + midIntensity * 0.35 + trebleIntensity * 0.3 + abs(rippleEffect3) * 0.4;

      // Add pulsing effect based on audio intensity and ripples
      intensityBoost *= 1.0 + 0.3 * sin(time * (2.0 + bassIntensity * 1.5)) * (1.0 + abs(rippleEffect1 * rippleEffect2) * 0.5);
    }

    // Apply boosted color
    vec3 boostedColor = finalColor * intensityBoost;

    // Enhanced rim lighting effect for stronger blue-purple glow like in the reference image
    float rimLight = 1.0 - max(0.0, dot(vNormal, vec3(0.0, 0.0, 1.0)));
    rimLight = pow(rimLight, 2.0) * 1.2; // Stronger power and intensity for more pronounced glow

    // Create color-specific glow that matches the reference image
    vec3 rimColor;

    if (audioState < 0.5) {
        // Inactive state - subtle blue glow
        rimColor = vec3(0.2, 0.4, 0.8) * rimLight;
    } else {
        // Active state - create blue-purple outer glow like in the reference image
        // Use the outer edge colors from our palette for the glow
        vec3 outerGlowColor = vec3(0.2, 0.4, 1.0); // Electric blue outer glow

        // Mix with some purple for the reference image look
        vec3 purpleGlowColor = vec3(0.5, 0.1, 0.9); // Purple glow component

        // Create dynamic glow color based on position and ripple effects
        float glowMix = 0.5 + 0.5 * sin(time * 0.5 + vUv.x * 5.0 + vUv.y * 3.0);
        rimColor = mix(outerGlowColor, purpleGlowColor, glowMix) * rimLight * 1.5;

        // Boost glow intensity based on audio
        rimColor *= 1.0 + bassIntensity * 0.5 + midIntensity * 0.3;
    }

    // Apply additional outer glow effect for the cloud-like appearance
    float edgeGlow = pow(rimLight, 1.5) * 0.8;
    vec3 edgeColor = vec3(0.3, 0.5, 1.0) * edgeGlow; // Blue-dominant edge glow

    // Final color with enhanced rim lighting and glow effects
    gl_FragColor = vec4(boostedColor + rimColor + edgeColor, 1.0);
  }
`;

// Create shader material with uniforms
const shaderMaterial = new THREE.ShaderMaterial({
  uniforms: {
    time: { value: 0 },
    bassIntensity: { value: 0 },
    midIntensity: { value: 0 },
    trebleIntensity: { value: 0 },
    isSpeaking: { value: 0 },
    isListening: { value: 0 },
    audioState: { value: 0 }, // Add audioState uniform (0=inactive, 1=listening, 2=speaking, 3=both)
    // Default palette (inactive state) - now with 7 colors
    color1: { value: new THREE.Color(palette[0]) },
    color2: { value: new THREE.Color(palette[1]) },
    color3: { value: new THREE.Color(palette[2]) },
    color4: { value: new THREE.Color(palette[3]) },
    color5: { value: new THREE.Color(palette[4]) },
    color6: { value: new THREE.Color(palette[5]) },
    color7: { value: new THREE.Color(palette[6]) },
    // User speaking palette - still 5 colors
    userColor1: { value: new THREE.Color(userSpeakingPalette[0]) },
    userColor2: { value: new THREE.Color(userSpeakingPalette[1]) },
    userColor3: { value: new THREE.Color(userSpeakingPalette[2]) },
    userColor4: { value: new THREE.Color(userSpeakingPalette[3]) },
    userColor5: { value: new THREE.Color(userSpeakingPalette[4]) },
    // Agent speaking palette - now with 7 colors
    agentColor1: { value: new THREE.Color(agentSpeakingPalette[0]) },
    agentColor2: { value: new THREE.Color(agentSpeakingPalette[1]) },
    agentColor3: { value: new THREE.Color(agentSpeakingPalette[2]) },
    agentColor4: { value: new THREE.Color(agentSpeakingPalette[3]) },
    agentColor5: { value: new THREE.Color(agentSpeakingPalette[4]) },
    agentColor6: { value: new THREE.Color(agentSpeakingPalette[5]) },
    agentColor7: { value: new THREE.Color(agentSpeakingPalette[6]) },
    // Both speaking palette - still 5 colors
    bothColor1: { value: new THREE.Color(bothSpeakingPalette[0]) },
    bothColor2: { value: new THREE.Color(bothSpeakingPalette[1]) },
    bothColor3: { value: new THREE.Color(bothSpeakingPalette[2]) },
    bothColor4: { value: new THREE.Color(bothSpeakingPalette[3]) },
    bothColor5: { value: new THREE.Color(bothSpeakingPalette[4]) }
  },
  vertexShader,
  fragmentShader,
  transparent: true,
  opacity: 0.0 // Start invisible
});

// Create the solid sphere mesh with shader material
const solidSphere = new THREE.Mesh(sphereGeometry, shaderMaterial);
solidSphere.renderOrder = 0; // Render before the mesh connections
solidSphere.visible = false; // Start invisible
scene.add(solidSphere);
solidSphereRef.current = solidSphere;

// No need for additional scaling since we already made the geometry smaller

// Mark as having custom shader
solidSphereRef.current.userData.hasCustomShader = true;

// Add ambient light for base illumination
const ambientLight = new THREE.AmbientLight(0x404040);
scene.add(ambientLight);

// Add directional light for better 3D definition
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(200, 200, 200); // Position light to match camera angle
scene.add(directionalLight);

// Initialize the start time
timeStartRef.current = Date.now();

// Animation function
const animate = () => {
  if (!sceneRef.current || !cameraRef.current || !rendererRef.current || !particleSystemRef.current) {
    return;
  }

  // Get the current time
  const elapsedTime = Date.now() - timeStartRef.current;
  const time = elapsedTime * 0.001; // Convert to seconds

  // Get the particle positions and colors
  const particles = particleSystemRef.current.geometry;
  const positions = particles.attributes.position.array as Float32Array;
  const initialPositions = particles.userData.initialPositions as Float32Array;
  const colors = particles.attributes.color.array as Float32Array;
  const latitudes = particles.userData.latitudes as Float32Array;
  const longitudes = particles.userData.longitudes as Float32Array;

  // Create simulated audio values for animation
  let bassIntensity, midIntensity, trebleIntensity;

  // Create audio simulation values with different intensities based on state
  const intensityMultiplier = isSpeaking ? 1.5 : 1.0;

  if (!isListening && !isSpeaking) {
    // When not in a call, use reduced but still dynamic audio values
    // This keeps the color shifting but reduces the impact on mesh wobble
    bassIntensity = (0.2 + 0.1 * Math.sin(time * 0.4)) * 0.5; // Reduced amplitude and frequency
    midIntensity = (0.2 + 0.1 * Math.sin(time * 0.5)) * 0.5;  // Reduced amplitude and frequency
    trebleIntensity = (0.2 + 0.1 * Math.sin(time * 0.6)) * 0.5; // Reduced amplitude and frequency
  } else {
    // In call: use normal dynamic audio simulation with full intensity
    bassIntensity = (0.4 + 0.3 * Math.sin(time * 0.8)) * intensityMultiplier;
    midIntensity = (0.4 + 0.3 * Math.sin(time * 1.2)) * intensityMultiplier;
    trebleIntensity = (0.4 + 0.3 * Math.sin(time * 1.5)) * intensityMultiplier;
  }

  // Keep the mesh stable without ripple effects
  for (let i = 0; i < particleCount; i++) {
    const j = i * 3;

    // Get the base position and use it directly
    const baseX = initialPositions[j];
    const baseY = initialPositions[j + 1];
    const baseZ = initialPositions[j + 2];

    // Keep the mesh stable - just use the original positions
    positions[j] = baseX;
    positions[j + 1] = baseY;
    positions[j + 2] = baseZ;
  }

  // Update the geometry
  particles.attributes.position.needsUpdate = true;

  // Update mesh particles if they exist
  if (connectionsRef.current) {
    // Update mesh particle positions to match main particles
    connectionsRef.current.geometry.setAttribute('position', particles.getAttribute('position'));
    connectionsRef.current.geometry.attributes.position.needsUpdate = true;
  }

  // We're using the palette and lerpColor function defined at the top of the file

  // Update particle colors based on position and time
  if (!isListening && !isSpeaking) {
    // Not in call: more varied color appearance with mesh visible
    for (let i = 0; i < particleCount; i++) {
      const j = i * 3;

      // Create a color pattern based on position focused on blues and purples
      const lat = latitudes[i];
      const lon = longitudes[i];

      // Use position to create color variation
      const colorVariation = (Math.sin(lat * 5) + Math.cos(lon * 6)) * 0.5 + 0.5;

      if (colorVariation < 0.33) {
        // Deep blue tones
        colors[j] = 0.0;      // R
        colors[j + 1] = 0.1;  // G
        colors[j + 2] = 0.4;  // B
      } else if (colorVariation < 0.66) {
        // Cyan/aqua tones
        colors[j] = 0.0;      // R
        colors[j + 1] = 0.5;  // G
        colors[j + 2] = 0.5;  // B
      } else {
        // Purple tones
        colors[j] = 0.3;      // R
        colors[j + 1] = 0.0;  // G
        colors[j + 2] = 0.5;  // B
      }
    }

    // Make connections visible when not in a call with blue/purple colors
    if (connectionsRef.current && connectionsRef.current.material instanceof THREE.LineBasicMaterial) {
      connectionsRef.current.material.opacity = 0.3; // Consistent opacity
      connectionsRef.current.material.color.set(0x3333aa); // Deep blue/purple connections
      connectionsRef.current.material.vertexColors = true; // Use vertex colors for more variety

      // Ensure consistent appearance
      connectionsRef.current.material.depthTest = false;
      connectionsRef.current.material.depthWrite = false;
    }

    // Make particles visible when not in a call
    if (particleSystemRef.current && particleSystemRef.current.material instanceof THREE.PointsMaterial) {
      particleSystemRef.current.material.size = 2.0; // Normal size particles
      particleSystemRef.current.material.opacity = 1.0; // Fully visible
    }

    // Completely hide the solid sphere when not in a call
    if (solidSphereRef.current) {
      // Immediately make the sphere invisible
      solidSphereRef.current.visible = false;

      // Also set opacity to 0 for when it becomes visible again
      if (solidSphereRef.current.material instanceof THREE.Material) {
        solidSphereRef.current.material.opacity = 0;
      }
    }
  } else {
    // In call: show both the solid sphere and the mesh with reduced opacity

    // Keep connections visible when conversation is active with the same appearance as inactive state
    if (connectionsRef.current && connectionsRef.current.material instanceof THREE.LineBasicMaterial) {
      // Keep the same opacity as inactive state
      connectionsRef.current.material.opacity = 0.3;
      connectionsRef.current.visible = true;

      // Use the same color as inactive state
      connectionsRef.current.material.color.set(0x3333aa); // Deep blue/purple connections
      connectionsRef.current.material.vertexColors = true;

      // Keep the same rendering properties
      connectionsRef.current.material.depthTest = false;
      connectionsRef.current.material.depthWrite = false;
      connectionsRef.current.renderOrder = 1; // Render after the solid sphere

      // Create distinctly different color patterns for the mesh compared to the sphere
      // Using different frequencies, phases, and pattern types
      const meshSpeed = isSpeaking ? 0.8 : 0.5; // Different speed than the sphere

      // Update connection colors with a unique pattern
      for (let i = 0; i < particleCount; i++) {
        const j = i * 3;

        // Get the latitude and longitude for this particle
        const lat = latitudes[i];
        const lon = longitudes[i];

        // Create complex patterns distinctly different from the sphere's waves
        // Use spirals, checkerboard patterns, and different frequencies

        // Spiral pattern that rotates around the sphere
        const spiral = 0.5 + 0.5 * Math.cos(lon * 8 + lat * 3 + time * meshSpeed);

        // Checkerboard pattern that creates distinct regions
        const checker = ((Math.floor(lat * 10) + Math.floor(lon * 10)) % 2) * 0.5;

        // Pulsing pattern based on distance from "equator"
        const pulse = 0.5 + 0.5 * Math.sin(Math.abs(lat - 0.5) * 15 + time * meshSpeed * 1.5);

        // Combine patterns with time-varying weights for dynamic transitions
        const weight1 = 0.5 + 0.5 * Math.sin(time * 0.3);
        const weight2 = 0.5 + 0.5 * Math.cos(time * 0.4);

        // Create two distinct pattern values for color selection
        const spiral1 = spiral * weight1 + checker * (1 - weight1);
        const spiral2 = pulse * weight2 + (1 - checker) * (1 - weight2);

        // Get colors from palette with smooth transitions
        const idx1 = Math.floor(spiral1 * palette.length) % palette.length;
        const idx2 = Math.floor(spiral2 * palette.length) % palette.length;

        // Create smooth transitions between colors
        const t1 = spiral1 % 1.0; // Fractional part for interpolation
        const t2 = spiral2 % 1.0;

        // Get colors from palette based on conversation state for more variety
        let color1, color2, color3, color4;

        if (isSpeaking && isListening) {
          // Both speaking - use the bothSpeakingPalette for yellow/orange/red
          const bothIdx1 = Math.floor(spiral1 * bothSpeakingPalette.length) % bothSpeakingPalette.length;
          const bothIdx2 = Math.floor(spiral2 * bothSpeakingPalette.length) % bothSpeakingPalette.length;

          color1 = bothSpeakingPalette[bothIdx1];
          color2 = bothSpeakingPalette[(bothIdx1 + 1) % bothSpeakingPalette.length];
          color3 = bothSpeakingPalette[bothIdx2];
          color4 = bothSpeakingPalette[(bothIdx2 + 1) % bothSpeakingPalette.length];
        } else if (isSpeaking) {
          // Agent speaking - use the agent palette for purple/pink colors
          const agentIdx1 = Math.floor(spiral1 * agentSpeakingPalette.length) % agentSpeakingPalette.length;
          const agentIdx2 = Math.floor(spiral2 * agentSpeakingPalette.length) % agentSpeakingPalette.length;

          color1 = agentSpeakingPalette[agentIdx1];
          color2 = agentSpeakingPalette[(agentIdx1 + 1) % agentSpeakingPalette.length];
          color3 = agentSpeakingPalette[agentIdx2];
          color4 = agentSpeakingPalette[(agentIdx2 + 1) % agentSpeakingPalette.length];
        } else if (isListening) {
          // User speaking - use the user palette for green/teal colors
          const userIdx1 = Math.floor(spiral1 * userSpeakingPalette.length) % userSpeakingPalette.length;
          const userIdx2 = Math.floor(spiral2 * userSpeakingPalette.length) % userSpeakingPalette.length;

          color1 = userSpeakingPalette[userIdx1];
          color2 = userSpeakingPalette[(userIdx1 + 1) % userSpeakingPalette.length];
          color3 = userSpeakingPalette[userIdx2];
          color4 = userSpeakingPalette[(userIdx2 + 1) % userSpeakingPalette.length];
        } else {
          // Otherwise use the regular palette with more variety
          color1 = palette[idx1];
          color2 = palette[(idx1 + 2) % palette.length]; // Skip one color for more contrast
          color3 = palette[idx2];
          color4 = palette[(idx2 + 3) % palette.length]; // Skip two colors for even more contrast
        }

        // Interpolate between colors with unique blending for the mesh
        const mixColor1 = new THREE.Color().lerpColors(color1, color2, t1);
        const mixColor2 = new THREE.Color().lerpColors(color3, color4, t2);

        // Create a third mix color using a different palette index for more variety
        let thirdColor;

        if (isSpeaking && isListening) {
          // Both speaking - use a bright purple color
          const bothIdx3 = Math.floor(Math.random() * agentSpeakingPalette.length);
          thirdColor = agentSpeakingPalette[bothIdx3]; // Use agent palette for purple focus
        } else if (isSpeaking) {
          // Agent speaking - use more purple colors
          const agentIdx3 = Math.floor(Math.random() * agentSpeakingPalette.length);
          thirdColor = agentSpeakingPalette[agentIdx3];
        } else if (isListening) {
          // User speaking - use more blue colors
          const userIdx3 = Math.floor(Math.random() * userSpeakingPalette.length);
          thirdColor = userSpeakingPalette[userIdx3];
        } else {
          // Otherwise use a color from the main palette that's different from the others
          const idx3 = (idx1 + 3) % palette.length; // Skip two colors for more contrast
          thirdColor = palette[idx3];
        }

        const mixColor3 = new THREE.Color().lerpColors(
          thirdColor,
          color4,
          0.3 + 0.2 * Math.sin(time * 0.5) // More dynamic blending
        );

        // Dynamic blending based on position and time
        // This creates patterns that are distinctly different from the sphere
        const blendFactor = 0.3 + 0.4 * Math.sin(lat * 8 - lon * 6 + time * 0.6);

        // Final color with three-way blending for more complex patterns
        const tempColor = new THREE.Color().lerpColors(mixColor1, mixColor2, blendFactor);
        const finalColor = new THREE.Color().lerpColors(tempColor, mixColor3, 0.3 + 0.2 * Math.cos(time * 0.7));

        // Aggressive color processing to absolutely prevent white in the mesh

        // First, ensure the color is never white or too light by capping brightness
        const maxBrightness = 0.7; // Lower cap to prevent white or near-white (1.0 would be white)
        const brightness = (finalColor.r + finalColor.g + finalColor.b) / 3;

        // Always apply some brightness reduction to ensure vibrant colors
        const brightnessScale = (brightness > maxBrightness)
          ? maxBrightness / brightness
          : Math.min(0.95, maxBrightness / Math.max(0.5, brightness)); // Still cap even if not too bright

        finalColor.r *= brightnessScale;
        finalColor.g *= brightnessScale;
        finalColor.b *= brightnessScale;

        // Ensure at least one color channel has strong saturation
        // This prevents gray/white colors by forcing color dominance
        const maxChannel = Math.max(finalColor.r, finalColor.g, finalColor.b);
        const saturationBoost = 1.5; // Stronger saturation boost
        const otherChannelReduction = 0.6; // More aggressive reduction of non-dominant channels

        if (maxChannel === finalColor.r) {
          // Red is dominant, boost it and reduce others more aggressively
          finalColor.r = Math.min(1.0, finalColor.r * saturationBoost);
          finalColor.g *= otherChannelReduction;
          finalColor.b *= otherChannelReduction;
        } else if (maxChannel === finalColor.g) {
          // Green is dominant, boost it and reduce others more aggressively
          finalColor.g = Math.min(1.0, finalColor.g * saturationBoost);
          finalColor.r *= otherChannelReduction;
          finalColor.b *= otherChannelReduction;
        } else {
          // Blue is dominant, boost it and reduce others more aggressively
          finalColor.b = Math.min(1.0, finalColor.b * saturationBoost);
          finalColor.r *= otherChannelReduction;
          finalColor.g *= otherChannelReduction;
        }

        // Final check - if the color is still too close to white, force it to a vibrant color
        const finalBrightness = (finalColor.r + finalColor.g + finalColor.b) / 3;
        const allChannelsHigh = finalColor.r > 0.7 && finalColor.g > 0.7 && finalColor.b > 0.7;

        if (finalBrightness > 0.75 || allChannelsHigh) {
          // Force to a color from our palette if still too bright or too white-ish
          const forcedColorIdx = Math.floor(Math.random() * palette.length);
          finalColor.copy(palette[forcedColorIdx]);
        }

        // Set the color for this particle
        colors[j] = finalColor.r;
        colors[j + 1] = finalColor.g;
        colors[j + 2] = finalColor.b;
      }
    }

    // Keep particles visible when conversation is active with the same appearance as inactive state
    if (particleSystemRef.current && particleSystemRef.current.material instanceof THREE.PointsMaterial) {
      particleSystemRef.current.material.opacity = 1.0; // Same opacity as inactive state
      particleSystemRef.current.visible = true;
      particleSystemRef.current.material.size = 2.0; // Normal size particles
    }

    // Show and update the solid sphere when in a call
    if (solidSphereRef.current) {
      // Make the sphere visible
      solidSphereRef.current.visible = true;

      // Update shader uniforms for the solid sphere
      if (solidSphereRef.current.material instanceof THREE.ShaderMaterial) {
        // Update time
        solidSphereRef.current.material.uniforms.time.value = time;

        // Update audio intensities
        solidSphereRef.current.material.uniforms.bassIntensity.value = bassIntensity;
        solidSphereRef.current.material.uniforms.midIntensity.value = midIntensity;
        solidSphereRef.current.material.uniforms.trebleIntensity.value = trebleIntensity;

        // Update speaking and listening states
        solidSphereRef.current.material.uniforms.isSpeaking.value = isSpeaking ? 1.0 : 0.0;
        solidSphereRef.current.material.uniforms.isListening.value = isListening ? 1.0 : 0.0;

        // Set audioState uniform (0=inactive, 1=listening, 2=speaking, 3=both)
        let audioState = 0;
        if (isListening && isSpeaking) {
          audioState = 3; // Both active
        } else if (isSpeaking) {
          audioState = 2; // Agent speaking
        } else if (isListening) {
          audioState = 1; // User speaking
        }
        solidSphereRef.current.material.uniforms.audioState.value = audioState;

        // Update opacity for fade in/out - slightly reduced to work well with visible mesh
        solidSphereRef.current.material.opacity = Math.min(0.7, solidSphereRef.current.material.opacity + 0.05);
      }

      // No need for additional geometry manipulation since the vertex shader handles the ripple effects
    }

  }

  // Update the colors
  particles.attributes.color.needsUpdate = true;

  // Update particle size for inactive state only (particles are hidden during active calls)
  if (particleSystemRef.current.material instanceof THREE.PointsMaterial) {
    if (!isListening && !isSpeaking) {
      // Not in call: normal sized particles with subtle pulsing
      const baseSize = 2.0;
      const pulseAmount = 0.3;
      particleSystemRef.current.material.size = baseSize + Math.sin(time * 0.5) * pulseAmount;

      // Make sure particles are visible when not in a call
      particleSystemRef.current.visible = true;
      particleSystemRef.current.material.opacity = 1.0;
    }
    // We don't update particle size during active calls since they're hidden
  }

  // --- Enhanced dynamic multi-axis rotation for the particle sphere ---
  const t = time; // seconds

  // Keep the rotation for both inactive and active states
  // Create more complex rotation patterns
  // Base rotation speed varies with audio intensity
  const baseRotationSpeed = 0.001 + 0.0005 * Math.sin(t * 0.3);
  const audioBoost = isListening || isSpeaking ? 2.0 : 1.0;

  // Y-axis: base rotation + audio-reactive movement
  const yRotationSpeed = baseRotationSpeed + bassIntensity * 0.001 * audioBoost;
  particleSystemRef.current.rotation.y += yRotationSpeed;

  // X-axis: complex oscillation pattern
  const xOscillation1 = 0.15 * Math.sin(t * 0.7);
  const xOscillation2 = 0.05 * Math.sin(t * 0.3 + Math.PI / 3) * midIntensity;
  particleSystemRef.current.rotation.x = xOscillation1 + xOscillation2;

  // Z-axis: organic movement with multiple frequencies
  const zOscillation1 = 0.05 * Math.cos(t * 0.5 + Math.PI / 4);
  const zOscillation2 = 0.03 * Math.sin(t * 0.2) * trebleIntensity;
  particleSystemRef.current.rotation.z = zOscillation1 + zOscillation2;

  // No special rotation handling needed for active/inactive states
  // We're using the same rotation logic for both states

  // Apply same rotation to mesh particles
  if (connectionsRef.current) {
    connectionsRef.current.rotation.copy(particleSystemRef.current.rotation);
  }

  // Render the scene
  rendererRef.current.render(sceneRef.current, cameraRef.current);

  // Request next frame
  frameIdRef.current = requestAnimationFrame(animate);
};

// Start animation
animate();

// Handle resize
const handleResize = () => {
  if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;

  const width = containerRef.current.clientWidth;
  const height = containerRef.current.clientHeight;

  cameraRef.current.aspect = width / height;
  cameraRef.current.updateProjectionMatrix();
  rendererRef.current.setSize(width, height);
};

window.addEventListener('resize', handleResize);

// Cleanup function
return () => {
  window.removeEventListener('resize', handleResize);

  if (frameIdRef.current) {
    cancelAnimationFrame(frameIdRef.current);
  }

  if (particleSystemRef.current) {
    particleSystemRef.current.geometry.dispose();
    if (particleSystemRef.current.material instanceof THREE.Material) {
      particleSystemRef.current.material.dispose();
    }
  }

  if (connectionsRef.current) {
    connectionsRef.current.geometry.dispose();
    if (connectionsRef.current.material instanceof THREE.Material) {
      connectionsRef.current.material.dispose();
    }
  }

  // Clean up solid sphere
  if (solidSphereRef.current) {
    solidSphereRef.current.geometry.dispose();
    if (solidSphereRef.current.material instanceof THREE.Material) {
      // Handle any type of material (ShaderMaterial or MeshStandardMaterial)
      solidSphereRef.current.material.dispose();
    }
  }

  if (rendererRef.current && containerRef.current) {
    containerRef.current.removeChild(rendererRef.current.domElement);
    rendererRef.current.dispose();
  }
};