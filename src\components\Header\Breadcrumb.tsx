import React from 'react';
import { ChevronRight } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  link: string;
  active?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({ items }) => {
  return (
    <div className="flex items-center text-sm">
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRight size={14} className="mx-2 text-gray-500" />
          )}
          <a 
            href={item.link}
            className={`hover:text-gray-300 transition-colors ${
              item.active ? 'text-white font-medium' : 'text-gray-400'
            }`}
          >
            {item.label}
          </a>
        </React.Fragment>
      ))}
    </div>
  );
};