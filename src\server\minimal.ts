import express from 'express';
import cors from 'cors';

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Mock data for testing
const mockAssistants = [
  {
    id: 'assistant_1',
    name: 'Customer Support Agent',
    firstMessage: 'Hello! How can I help you today?',
    systemMessage: 'You are a helpful customer support representative.',
    model: {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 500,
    },
    voice: {
      provider: 'elevenlabs',
      voiceId: 'default',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'assistant_2',
    name: 'Sales Representative',
    firstMessage: 'Hi! I\'d love to learn more about your needs.',
    systemMessage: 'You are a skilled sales representative.',
    model: {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.8,
      maxTokens: 600,
    },
    voice: {
      provider: 'elevenlabs',
      voiceId: 'chris',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

const mockTemplates = [
  {
    id: 'customer-support',
    name: 'Customer Support Agent',
    description: 'A helpful customer support assistant',
    category: 'support',
    config: {
      name: 'Customer Support Assistant',
      firstMessage: 'Hello! How can I help you today?',
      systemMessage: 'You are a helpful customer support representative.',
    },
    tags: ['support', 'customer-service'],
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'sales-agent',
    name: 'Sales Representative',
    description: 'A persuasive sales assistant',
    category: 'sales',
    config: {
      name: 'Sales Assistant',
      firstMessage: 'Hi! I\'d love to learn more about your needs.',
      systemMessage: 'You are a skilled sales representative.',
    },
    tags: ['sales', 'lead-generation'],
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Vapi API Wrapper Server', status: 'running' });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      services: {
        calls: true,
        assistants: true,
        phoneNumbers: true,
        templates: true,
        analytics: true,
      },
    },
    timestamp: new Date().toISOString(),
  });
});

// Assistants routes
app.get('/api/assistants', (req, res) => {
  res.json({
    success: true,
    data: mockAssistants,
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/assistants/:id', (req, res) => {
  const assistant = mockAssistants.find(a => a.id === req.params.id);
  if (!assistant) {
    return res.status(404).json({
      success: false,
      error: 'Assistant not found',
      timestamp: new Date().toISOString(),
    });
  }
  res.json({
    success: true,
    data: assistant,
    timestamp: new Date().toISOString(),
  });
});

app.post('/api/assistants', (req, res) => {
  const newAssistant = {
    id: `assistant_${Date.now()}`,
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  mockAssistants.push(newAssistant);
  res.status(201).json({
    success: true,
    data: newAssistant,
    timestamp: new Date().toISOString(),
  });
});

app.patch('/api/assistants/:id', (req, res) => {
  const index = mockAssistants.findIndex(a => a.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({
      success: false,
      error: 'Assistant not found',
      timestamp: new Date().toISOString(),
    });
  }
  
  mockAssistants[index] = {
    ...mockAssistants[index],
    ...req.body,
    updatedAt: new Date().toISOString(),
  };
  
  res.json({
    success: true,
    data: mockAssistants[index],
    timestamp: new Date().toISOString(),
  });
});

app.delete('/api/assistants/:id', (req, res) => {
  const index = mockAssistants.findIndex(a => a.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({
      success: false,
      error: 'Assistant not found',
      timestamp: new Date().toISOString(),
    });
  }
  
  const deleted = mockAssistants.splice(index, 1)[0];
  res.json({
    success: true,
    data: deleted,
    timestamp: new Date().toISOString(),
  });
});

// Templates routes
app.get('/api/templates', (req, res) => {
  res.json({
    success: true,
    data: mockTemplates,
    timestamp: new Date().toISOString(),
  });
});

app.post('/api/templates/:id/create-assistant', (req, res) => {
  const template = mockTemplates.find(t => t.id === req.params.id);
  if (!template) {
    return res.status(404).json({
      success: false,
      error: 'Template not found',
      timestamp: new Date().toISOString(),
    });
  }
  
  const newAssistant = {
    id: `assistant_${Date.now()}`,
    ...template.config,
    ...req.body,
    model: {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 500,
    },
    voice: {
      provider: 'elevenlabs',
      voiceId: 'default',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  mockAssistants.push(newAssistant);
  res.status(201).json({
    success: true,
    data: newAssistant,
    timestamp: new Date().toISOString(),
  });
});

const port = 3001;
app.listen(port, () => {
  console.log(`🚀 Minimal Vapi API Server running on port ${port}`);
  console.log(`📊 Frontend: http://localhost:5174`);
  console.log(`🔗 Health check: http://localhost:${port}/api/health`);
});
