import React, { useState } from 'react';
import { Plus, Search, AlertCircle, Settings, Trash2, Edit, ExternalLink, Code, Phone, PhoneOff, Hash, MessageSquare, FileSearch, Calendar, User, Globe, FileText } from 'lucide-react';
import { Modal } from '../components/Modal';
import { ApiKeySetup } from '../components/ApiKeySetup';
import { useTools } from '../hooks/useTools';
import { Tool } from '../services/api';

export const Tools: React.FC = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [hasApiKey, setHasApiKey] = useState(false);

  // Form state for creating/editing tools
  const [toolType, setToolType] = useState<Tool['type']>('function');
  const [toolName, setToolName] = useState('');
  const [functionName, setFunctionName] = useState('');
  const [functionDescription, setFunctionDescription] = useState('');
  const [serverUrl, setServerUrl] = useState('');
  const [isAsync, setIsAsync] = useState(false);

  // Check for API key on mount
  React.useEffect(() => {
    const apiKey = import.meta.env.VITE_VAPI_API_KEY || localStorage.getItem('vapi_api_key');
    setHasApiKey(!!apiKey);
  }, []);

  const {
    tools,
    loading,
    error,
    createTool,
    updateTool,
    deleteTool,
    getToolTypeIcon,
    getToolTypeColor,
    formatToolName,
    formatToolDescription,
    clearError,
  } = useTools();

  // Filter tools based on search query and type
  const filteredTools = tools.filter(tool => {
    const matchesSearch = !searchQuery ||
      formatToolName(tool).toLowerCase().includes(searchQuery.toLowerCase()) ||
      formatToolDescription(tool).toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.id.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = typeFilter === 'all' || tool.type === typeFilter;

    return matchesSearch && matchesType;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Get tool type icon component
  const getToolIcon = (type: Tool['type']) => {
    switch (type) {
      case 'function':
        return <Code size={16} className="text-blue-400" />;
      case 'transferCall':
        return <Phone size={16} className="text-green-400" />;
      case 'endCall':
        return <PhoneOff size={16} className="text-red-400" />;
      case 'dtmf':
        return <Hash size={16} className="text-yellow-400" />;
      case 'sms':
        return <MessageSquare size={16} className="text-purple-400" />;
      case 'query':
        return <FileSearch size={16} className="text-orange-400" />;
      case 'google.calendar.availability.check':
      case 'google.calendar.event.create':
      case 'ghl.check.availability':
      case 'ghl.create.event':
        return <Calendar size={16} className="text-blue-400" />;
      case 'google.sheets.row.append':
        return <FileText size={16} className="text-green-400" />;
      case 'slack.message.send':
        return <MessageSquare size={16} className="text-purple-400" />;
      case 'mcp':
        return <Globe size={16} className="text-indigo-400" />;
      case 'ghl.contact.get':
      case 'ghl.contact.create':
        return <User size={16} className="text-orange-400" />;
      default:
        return <Settings size={16} className="text-gray-400" />;
    }
  };

  // Handle tool deletion
  const handleDeleteTool = async (id: string) => {
    try {
      await deleteTool(id);
      setShowDeleteConfirm(null);
    } catch (err) {
      console.error('Failed to delete tool:', err);
    }
  };

  // Reset form state
  const resetForm = () => {
    setToolType('function');
    setToolName('');
    setFunctionName('');
    setFunctionDescription('');
    setServerUrl('');
    setIsAsync(false);
  };

  // Handle tool creation
  const handleCreateTool = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!functionName.trim()) return;

    try {
      const toolData: any = {
        type: toolType,
        name: toolName || undefined,
      };

      if (toolType === 'function') {
        toolData.function = {
          name: functionName,
          description: functionDescription || undefined,
          parameters: {
            type: 'object',
            properties: {},
            required: [],
          },
        };

        if (serverUrl) {
          toolData.server = { url: serverUrl };
        }

        toolData.async = isAsync;
      }

      await createTool(toolData);
      setIsCreateModalOpen(false);
      resetForm();
    } catch (err) {
      console.error('Failed to create tool:', err);
    }
  };

  // Handle tool editing
  const handleEditTool = (tool: Tool) => {
    setSelectedTool(tool);
    setToolType(tool.type);
    setToolName(tool.name || '');
    setFunctionName(tool.function?.name || '');
    setFunctionDescription(tool.function?.description || '');
    setServerUrl(tool.server?.url || '');
    setIsAsync(tool.async || false);
    setIsEditModalOpen(true);
  };

  // Handle tool update
  const handleUpdateTool = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedTool || !functionName.trim()) return;

    try {
      const toolData: any = {
        type: toolType,
        name: toolName || undefined,
      };

      if (toolType === 'function') {
        toolData.function = {
          name: functionName,
          description: functionDescription || undefined,
          parameters: selectedTool.function?.parameters || {
            type: 'object',
            properties: {},
            required: [],
          },
        };

        if (serverUrl) {
          toolData.server = { url: serverUrl };
        }

        toolData.async = isAsync;
      }

      await updateTool(selectedTool.id, toolData);
      setIsEditModalOpen(false);
      setSelectedTool(null);
      resetForm();
    } catch (err) {
      console.error('Failed to update tool:', err);
    }
  };

  // Show API key setup if no key is found
  if (!hasApiKey) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0F0F0F] text-white p-6">
        <ApiKeySetup onApiKeySet={() => setHasApiKey(true)} />
      </div>
    );
  }

  return (
    <div className="flex-1 bg-[#0F0F0F] text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold mb-1">Tools</h1>
            <p className="text-gray-400">Manage your Vapi tools and integrations</p>
          </div>
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
          >
            <Plus size={16} />
            Create tool
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-900/20 border border-red-800 rounded-lg flex items-center gap-2">
            <AlertCircle size={18} className="text-red-400" />
            <span className="text-red-400">{error}</span>
            <button
              onClick={clearError}
              className="ml-auto text-red-400 hover:text-red-300"
            >
              ×
            </button>
          </div>
        )}

        {/* Filters */}
        <div className="flex flex-wrap gap-2 mb-6">
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-1 bg-[#1A1A1A] border border-gray-800 rounded-full text-sm text-gray-400 hover:bg-[#252525] transition-colors focus:outline-none focus:border-gray-700"
          >
            <option value="all">All types</option>
            <optgroup label="Built-in Tools">
              <option value="function">Custom Functions</option>
              <option value="transferCall">Transfer Call</option>
              <option value="endCall">End Call</option>
              <option value="dtmf">DTMF</option>
              <option value="sms">SMS</option>
              <option value="query">Query</option>
            </optgroup>
            <optgroup label="Google Integrations">
              <option value="google.calendar.availability.check">Google Calendar - Check Availability</option>
              <option value="google.calendar.event.create">Google Calendar - Create Event</option>
              <option value="google.sheets.row.append">Google Sheets - Add Row</option>
            </optgroup>
            <optgroup label="Communication Tools">
              <option value="slack.message.send">Slack - Send Message</option>
            </optgroup>
            <optgroup label="GoHighLevel (GHL)">
              <option value="ghl.contact.get">GHL - Get Contact</option>
              <option value="ghl.contact.create">GHL - Create Contact</option>
              <option value="ghl.check.availability">GHL - Check Availability</option>
              <option value="ghl.create.event">GHL - Create Event</option>
            </optgroup>
            <optgroup label="Automation Platforms">
              <option value="mcp">MCP (Model Context Protocol)</option>
            </optgroup>
          </select>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search tools by name, description, or ID..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
          />
        </div>

        {loading && filteredTools.length === 0 ? (
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-8 text-center">
            <div className="animate-spin w-6 h-6 border-2 border-gray-600 border-t-white rounded-full mx-auto mb-2"></div>
            <p className="text-gray-400">Loading tools...</p>
          </div>
        ) : filteredTools.length > 0 ? (
          /* Tools Grid */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTools.map((tool) => (
              <div
                key={tool.id}
                className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-4 hover:border-gray-700 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {getToolIcon(tool.type)}
                    <span className={`px-2 py-0.5 rounded-full text-xs capitalize ${getToolTypeColor(tool.type)}`}>
                      {tool.type}
                    </span>
                  </div>
                  <div className="flex gap-1">
                    <button
                      className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                      onClick={() => handleEditTool(tool)}
                      title="Edit tool"
                    >
                      <Edit size={14} className="text-gray-400" />
                    </button>
                    <button
                      className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                      onClick={() => setShowDeleteConfirm(tool.id)}
                      title="Delete tool"
                    >
                      <Trash2 size={14} className="text-red-400" />
                    </button>
                  </div>
                </div>

                <h3 className="font-medium text-gray-200 mb-1">
                  {formatToolName(tool)}
                </h3>

                <p className="text-sm text-gray-400 mb-3 line-clamp-2">
                  {formatToolDescription(tool)}
                </p>

                {tool.server?.url && (
                  <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
                    <ExternalLink size={12} />
                    <span className="truncate">{tool.server.url}</span>
                  </div>
                )}

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Created {formatDate(tool.createdAt)}</span>
                  <span className="text-gray-600">ID: {tool.id.slice(0, 8)}...</span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* Empty state */
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-12 text-center">
            <div className="w-12 h-12 bg-[#252525] rounded-lg flex items-center justify-center mx-auto mb-4">
              <Settings size={24} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {searchQuery || typeFilter !== 'all'
                ? 'No tools found'
                : 'No tools created'
              }
            </h3>
            <p className="text-sm text-gray-400 mb-6">
              {searchQuery || typeFilter !== 'all'
                ? 'No tools match your search criteria or filters.'
                : 'You don\'t have any tools in your Vapi account yet.'
              }
            </p>
            {!searchQuery && typeFilter === 'all' && (
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="px-4 py-2 bg-[#252525] text-white rounded-lg text-sm hover:bg-[#333333] transition-colors inline-flex items-center gap-2"
              >
                <Plus size={16} />
                Create your first tool
              </button>
            )}
          </div>
        )}
      </div>

      {/* Create Tool Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          resetForm();
        }}
        title="Create Tool"
      >
        <form onSubmit={handleCreateTool} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Tool Type
            </label>
            <select
              value={toolType}
              onChange={(e) => setToolType(e.target.value as Tool['type'])}
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm focus:outline-none focus:border-gray-700 transition-colors"
            >
              <optgroup label="Built-in Tools">
                <option value="function">Custom Function</option>
                <option value="transferCall">Transfer Call</option>
                <option value="endCall">End Call</option>
                <option value="dtmf">DTMF</option>
                <option value="sms">SMS</option>
                <option value="query">Query</option>
              </optgroup>
              <optgroup label="Google Integrations">
                <option value="google.calendar.availability.check">Google Calendar - Check Availability</option>
                <option value="google.calendar.event.create">Google Calendar - Create Event</option>
                <option value="google.sheets.row.append">Google Sheets - Add Row</option>
              </optgroup>
              <optgroup label="Communication Tools">
                <option value="slack.message.send">Slack - Send Message</option>
              </optgroup>
              <optgroup label="GoHighLevel (GHL)">
                <option value="ghl.contact.get">GHL - Get Contact</option>
                <option value="ghl.contact.create">GHL - Create Contact</option>
                <option value="ghl.check.availability">GHL - Check Availability</option>
                <option value="ghl.create.event">GHL - Create Event</option>
              </optgroup>
              <optgroup label="Automation Platforms">
                <option value="mcp">MCP (Model Context Protocol)</option>
              </optgroup>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Tool Name (Optional)
            </label>
            <input
              type="text"
              value={toolName}
              onChange={(e) => setToolName(e.target.value)}
              placeholder="e.g., Weather Tool"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
            />
          </div>

          {toolType === 'function' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Function Name *
                </label>
                <input
                  type="text"
                  value={functionName}
                  onChange={(e) => setFunctionName(e.target.value)}
                  placeholder="e.g., get_weather"
                  className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={functionDescription}
                  onChange={(e) => setFunctionDescription(e.target.value)}
                  placeholder="Describe what this function does..."
                  className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors min-h-[80px]"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Server URL
                </label>
                <input
                  type="url"
                  value={serverUrl}
                  onChange={(e) => setServerUrl(e.target.value)}
                  placeholder="https://your-api.com/webhook"
                  className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
                />
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="async"
                  checked={isAsync}
                  onChange={(e) => setIsAsync(e.target.checked)}
                  className="rounded border-gray-800 bg-[#0F0F0F] text-white focus:ring-gray-700"
                />
                <label htmlFor="async" className="text-sm text-gray-300">
                  Asynchronous execution
                </label>
              </div>
            </>
          )}

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setIsCreateModalOpen(false);
                resetForm();
              }}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || (toolType === 'function' && !functionName.trim())}
              className="px-4 py-2 bg-white text-black text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Creating...' : 'Create Tool'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Edit Tool Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedTool(null);
          resetForm();
        }}
        title="Edit Tool"
      >
        <form onSubmit={handleUpdateTool} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Tool Type
            </label>
            <select
              value={toolType}
              onChange={(e) => setToolType(e.target.value as Tool['type'])}
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm focus:outline-none focus:border-gray-700 transition-colors"
              disabled
            >
              <optgroup label="Built-in Tools">
                <option value="function">Custom Function</option>
                <option value="transferCall">Transfer Call</option>
                <option value="endCall">End Call</option>
                <option value="dtmf">DTMF</option>
                <option value="sms">SMS</option>
                <option value="query">Query</option>
              </optgroup>
              <optgroup label="Google Integrations">
                <option value="google.calendar.availability.check">Google Calendar - Check Availability</option>
                <option value="google.calendar.event.create">Google Calendar - Create Event</option>
                <option value="google.sheets.row.append">Google Sheets - Add Row</option>
              </optgroup>
              <optgroup label="Communication Tools">
                <option value="slack.message.send">Slack - Send Message</option>
              </optgroup>
              <optgroup label="GoHighLevel (GHL)">
                <option value="ghl.contact.get">GHL - Get Contact</option>
                <option value="ghl.contact.create">GHL - Create Contact</option>
                <option value="ghl.check.availability">GHL - Check Availability</option>
                <option value="ghl.create.event">GHL - Create Event</option>
              </optgroup>
              <optgroup label="Automation Platforms">
                <option value="mcp">MCP (Model Context Protocol)</option>
              </optgroup>
            </select>
            <p className="text-xs text-gray-400 mt-1">Tool type cannot be changed after creation</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Tool Name (Optional)
            </label>
            <input
              type="text"
              value={toolName}
              onChange={(e) => setToolName(e.target.value)}
              placeholder="e.g., Weather Tool"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
            />
          </div>

          {toolType === 'function' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Function Name *
                </label>
                <input
                  type="text"
                  value={functionName}
                  onChange={(e) => setFunctionName(e.target.value)}
                  placeholder="e.g., get_weather"
                  className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={functionDescription}
                  onChange={(e) => setFunctionDescription(e.target.value)}
                  placeholder="Describe what this function does..."
                  className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors min-h-[80px]"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Server URL
                </label>
                <input
                  type="url"
                  value={serverUrl}
                  onChange={(e) => setServerUrl(e.target.value)}
                  placeholder="https://your-api.com/webhook"
                  className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
                />
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="edit-async"
                  checked={isAsync}
                  onChange={(e) => setIsAsync(e.target.checked)}
                  className="rounded border-gray-800 bg-[#0F0F0F] text-white focus:ring-gray-700"
                />
                <label htmlFor="edit-async" className="text-sm text-gray-300">
                  Asynchronous execution
                </label>
              </div>
            </>
          )}

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setIsEditModalOpen(false);
                setSelectedTool(null);
                resetForm();
              }}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || (toolType === 'function' && !functionName.trim())}
              className="px-4 py-2 bg-white text-black text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Updating...' : 'Update Tool'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(null)}
        title="Delete Tool"
      >
        <div className="space-y-4">
          <p className="text-gray-300">
            Are you sure you want to delete this tool? This action cannot be undone and will remove the tool from your Vapi account.
          </p>
          <div className="flex justify-end gap-3">
            <button
              onClick={() => setShowDeleteConfirm(null)}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => showDeleteConfirm && handleDeleteTool(showDeleteConfirm)}
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
