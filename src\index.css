@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #1A1A1A;
}

::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 5px;
  border: 2px solid #1A1A1A;
}

::-webkit-scrollbar-thumb:hover {
  background: #404040;
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #333333 #1A1A1A;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes soundBar {
  0% { transform: scaleY(0.1); }
  20% { transform: scaleY(0.8); }
  40% { transform: scaleY(0.4); }
  60% { transform: scaleY(1); }
  80% { transform: scaleY(0.3); }
  100% { transform: scaleY(0.1); }
}

@layer utilities {
  .animate-sound-bar {
    animation: soundBar 1.2s ease-in-out infinite;
  }
  .animate-pulse-slow {
    animation: pulse 3s ease-in-out infinite;
  }
  .animate-rotate-slow {
    animation: rotate 12s linear infinite;
  }
}