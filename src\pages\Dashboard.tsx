import React from 'react';
import { BarChart3, Clock, DollarSign, Users2, ChevronDown, ChevronRight } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    label: string;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon, trend }) => (
  <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-4">
    <div className="flex items-center justify-between mb-4">
      <span className="text-sm text-gray-400">{title}</span>
      <div className="p-2 bg-[#0F0F0F] rounded-lg">
        {icon}
      </div>
    </div>
    <div className="text-2xl font-semibold mb-2">{value}</div>
    {trend && (
      <div className="flex items-center text-sm">
        <span className={trend.value >= 0 ? 'text-green-500' : 'text-red-500'}>
          {trend.value >= 0 ? '+' : ''}{trend.value}%
        </span>
        <span className="text-gray-400 ml-1">vs {trend.label}</span>
      </div>
    )}
  </div>
);

interface Agent {
  name: string;
  calls: number;
  minutes: number;
  credits: number;
}

const topAgents: Agent[] = [
  { name: 'Secretary/Gatekeeper', calls: 117, minutes: 20, credits: 8520 },
  { name: 'Tom - Atlas Construction', calls: 42, minutes: 20, credits: 7003 },
  { name: 'Outbound', calls: 29, minutes: 28, credits: 8696 }
];

export const Dashboard: React.FC = () => {
  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <div className="text-sm text-gray-400">My Workspace</div>
            <h1 className="text-2xl font-semibold">Good evening, Max</h1>
          </div>
          <div className="flex items-center gap-3">
            <button className="px-3 py-1.5 bg-[#1A1A1A] text-sm rounded-lg hover:bg-[#252525] transition-colors flex items-center gap-2">
              All agents
              <ChevronDown size={16} />
            </button>
            <button className="px-3 py-1.5 bg-[#1A1A1A] text-sm rounded-lg hover:bg-[#252525] transition-colors flex items-center gap-2">
              Last month
              <ChevronDown size={16} />
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <StatsCard
            title="Number of calls"
            value="288"
            icon={<BarChart3 size={20} className="text-blue-500" />}
            trend={{ value: 12, label: 'last month' }}
          />
          <StatsCard
            title="Average duration"
            value="0:26"
            icon={<Clock size={20} className="text-green-500" />}
            trend={{ value: -5, label: 'last month' }}
          />
          <StatsCard
            title="Total cost"
            value="42,656 credits"
            icon={<DollarSign size={20} className="text-purple-500" />}
            trend={{ value: 8, label: 'last month' }}
          />
          <StatsCard
            title="Average cost"
            value="148 credits/call"
            icon={<Users2 size={20} className="text-orange-500" />}
            trend={{ value: -2, label: 'last month' }}
          />
        </div>

        {/* Charts */}
        <div className="grid grid-cols-2 gap-6 mb-6">
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-6">
            <h2 className="text-lg font-medium mb-4">Call Volume</h2>
            <div className="h-[300px] flex items-end">
              {/* Placeholder for call volume chart */}
              <div className="flex-1 flex items-end justify-between gap-2">
                {[10, 25, 15, 40, 30, 60, 45, 35, 20, 50, 40, 30].map((height, i) => (
                  <div
                    key={i}
                    className="w-full bg-purple-500/20 rounded-t-sm hover:bg-purple-500/30 transition-colors cursor-pointer relative group"
                    style={{ height: `${height}%` }}
                  >
                    <div className="absolute bottom-full mb-1 left-1/2 -translate-x-1/2 bg-white text-black text-xs px-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                      {height}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-6">
            <h2 className="text-lg font-medium mb-4">Success Rate</h2>
            <div className="h-[300px] flex items-end">
              {/* Placeholder for success rate chart */}
              <div className="flex-1 flex items-end justify-between gap-2">
                {[80, 85, 75, 90, 88, 92, 87, 89, 86, 91, 88, 90].map((height, i) => (
                  <div
                    key={i}
                    className="w-full bg-green-500/20 rounded-t-sm hover:bg-green-500/30 transition-colors cursor-pointer relative group"
                    style={{ height: `${height}%` }}
                  >
                    <div className="absolute bottom-full mb-1 left-1/2 -translate-x-1/2 bg-white text-black text-xs px-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                      {height}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Most Called Agents */}
        <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium">Most called agents</h2>
            <button className="text-sm text-gray-400 hover:text-white transition-colors flex items-center gap-1">
              See all 5 agents
              <ChevronRight size={16} />
            </button>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-400">
                  <th className="pb-3 font-normal">Agent name</th>
                  <th className="pb-3 font-normal">Number of calls</th>
                  <th className="pb-3 font-normal">Call minutes</th>
                  <th className="pb-3 font-normal">Credits spent</th>
                </tr>
              </thead>
              <tbody>
                {topAgents.map((agent, index) => (
                  <tr key={index} className="border-t border-gray-800">
                    <td className="py-3">{agent.name}</td>
                    <td className="py-3">{agent.calls}</td>
                    <td className="py-3">{agent.minutes}</td>
                    <td className="py-3">{agent.credits.toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};